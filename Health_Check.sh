#!/bin/bash

# 定义变量
START_IP="********"
END_IP="**********"
USERNAME="root"

# 清理旧文件
rm -rf error_Check* scan_Check*

# 获取当前时间戳并构建输出文件名
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
SCAN_RESULTS="./scan_Check_$TIMESTAMP.txt"
ERROR_LOG="./error_Check_$TIMESTAMP.txt"

# 提取起始和结束的IP地址的最后一段及基础IP地址
start=$(echo $START_IP | awk -F. '{print $4}')
end=$(echo $END_IP | awk -F. '{print $4}')
base_ip=$(echo $START_IP | awk -F. '{print $1"."$2"."$3}')

# 计算日期相关正则表达式
today=$(date +"%b %e")
yesterday=$(date -d "1 day ago" +"%b %e")
day_before_yesterday=$(date -d "2 days ago" +"%b %e")
recent_days_regex="$today|$yesterday|$day_before_yesterday"

# 并发控制参数
max_concurrent=50
active_jobs=0

# 循环遍历IP段
for i in $(seq $start $end); do
    ip=$(printf "%d" "$i")
    FULL_IP="${base_ip}.${ip}"
    
    # 将任务放入后台执行
	 {
echo "正在检查 $FULL_IP..."

ssh_output=$(ssh \
-o ConnectTimeout=10 \
-o StrictHostKeyChecking=no \
-o BatchMode=yes \
$USERNAME@$FULL_IP \
"bash /root/NvidiaGPU_Check.sh 2>&1 || true" 2>&1)

exit_status=$?

if [ $exit_status -eq 0 ]; then
  gpu_check_output=$(echo "$ssh_output")

  if [ -n "$gpu_check_output" ]; then
    echo "在 $FULL_IP (NvidiaGPU_Check)中找到: $gpu_check_output" >> "$SCAN_RESULTS"
  fi

  if [ -z "$gpu_check_output" ]; then
    echo "$(date +%Y-%m-%d_%H:%M:%S) - 对于 $FULL_IP 没有匹配的输出。" >> "$ERROR_LOG"
  fi
else
  echo "$(date +%Y-%m-%d_%H:%M:%S) - 连接失败或 $FULL_IP 出现错误: $ssh_output" >> "$ERROR_LOG"
fi

active_jobs=$((active_jobs - 1))
} &

    active_jobs=$((active_jobs + 1))

    if [ $active_jobs -ge $max_concurrent ]; then
        wait -n  # 等待任意一个子进程完成
    fi
done


# 等待所有剩余的后台任务完成
wait

echo "完成"
