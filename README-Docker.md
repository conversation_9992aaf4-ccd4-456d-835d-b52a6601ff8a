# Docker版本的NCCL测试工具

这个版本允许你在Docker容器中直接运行NCCL测试，而不需要Kubernetes集群。

## 前提条件

1. **Docker环境**：确保安装了Docker和Docker Compose
2. **NVIDIA Docker支持**：安装nvidia-docker2
3. **GPU驱动**：确保NVIDIA驱动已安装
4. **网络环境**：确保节点间可以通过InfiniBand或以太网通信

## 安装nvidia-docker2

```bash
# Ubuntu/Debian
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

## 使用方法

### 方法1：使用Docker Compose（推荐）

1. **修改配置**：编辑`docker-compose.yml`中的参数
```yaml
command: >
  python nccl-test-docker.py
  --gpu_num 8                    # GPU数量
  --ib_num 1                     # IB设备数量
  --nodes hs[01-04]              # 节点列表
  --image your-nccl-image:tag    # NCCL测试镜像
  --mode random                  # 测试模式
  --NCCL_ALGO Tree,Ring         # NCCL算法
```

2. **运行测试**：
```bash
docker-compose up --build
```

### 方法2：直接使用Docker

1. **构建镜像**：
```bash
docker build -t nccl-test-runner .
```

2. **运行测试**：
```bash
docker run --rm --privileged --network=host \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v $(pwd):/app \
  --gpus all \
  nccl-test-runner \
  python nccl-test-docker.py \
  --gpu_num 8 \
  --ib_num 1 \
  --nodes hs[01-04] \
  --image dockerhub.aicp.local/aicp-common/aicp_common/nccl_test:v3.0 \
  --mode random
```

### 方法3：在主机上直接运行

如果你不想使用Docker容器来运行脚本，也可以直接在主机上运行：

```bash
# 安装依赖
pip install pyyaml

# 运行测试
python nccl-test-docker.py \
  --gpu_num 8 \
  --ib_num 1 \
  --nodes hs[01-04] \
  --image dockerhub.aicp.local/aicp-common/aicp_common/nccl_test:v3.0 \
  --mode random
```

## 参数说明

- `--gpu_num`: 每个节点使用的GPU数量
- `--ib_num`: 每个节点使用的InfiniBand设备数量
- `--nodes`: 节点列表，支持范围表示法如`hs[01-04]`
- `--image`: NCCL测试镜像
- `--mode`: 测试模式
  - `random`: 测试1, 2, 4, 8...个节点的配置
  - 数字: 指定每组节点数量
- `--NCCL_ALGO`: NCCL算法，如`Tree,Ring,CollnetDirect`
- `--NCCL_NVLS_ENABLE`: 是否启用NVLS (0/1)
- `--NCCL_NET_GDR_LEVEL`: GDR级别
- `--NCCL_IB_QPS_PER_CONNECTION`: IB连接数

## 输出

测试完成后会生成一个Markdown格式的报告文件：
- `docker-test-report-YYYYMMDDHHMMSS.md`

报告包含：
- 测试摘要表格
- 详细的测试日志
- 带宽性能数据

## 注意事项

1. **权限要求**：需要`--privileged`模式来访问GPU和网络设备
2. **网络模式**：使用`--network=host`确保容器可以访问主机网络
3. **Docker Socket**：需要挂载`/var/run/docker.sock`来启动子容器
4. **GPU访问**：确保nvidia-docker正确配置
5. **节点通信**：确保所有节点间网络连通

## 故障排除

### GPU不可见
```bash
# 检查nvidia-docker
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### 网络连接问题
```bash
# 检查节点间连通性
ping <node-ip>
```

### 权限问题
```bash
# 确保Docker有足够权限
sudo usermod -aG docker $USER
```

## 与原Kubernetes版本的区别

| 特性 | Kubernetes版本 | Docker版本 |
|------|----------------|------------|
| 集群管理 | 需要K8s集群 | 直接使用Docker |
| 资源调度 | K8s自动调度 | 手动指定节点 |
| 扩展性 | 支持大规模集群 | 适合中小规模 |
| 部署复杂度 | 需要K8s环境 | 只需要Docker |
| 监控 | K8s原生监控 | 简单日志输出 |
| 资源隔离 | 容器级隔离 | 进程级隔离 | 