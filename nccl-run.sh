#!/bin/bash

# 设置文件描述符限制
ulimit -n 1048576

# 计算 my_hostfile 中的 IP 地址数量
num_hosts=$(wc -l < my_hostfile)
np=$((num_hosts * 8))

# 设置环境变量（如果需要）
export OMPI_MCA_rmaps_base_oversubscribe=1  # 允许过度订阅
export OMPI_MCA_btl_base_verbose=100       # 设置 BTL 模块的调试级别
export OMPI_MCA_plm_base_verbose=100       # 设置 PLM 模块的调试级别
export OMPI_MCA_orte_base_help_aggregate=0 # 禁用汇总帮助信息
export OMPI_MCA_odebug=1                   # 开启调试模式
export NCCL_DEBUG=INFO  # 设置 NCCL 调试级别为 INFO
export NCCL_DEBUG_SUBSYS=ALL  # 设置调试子系统为 ALL

# 运行 mpirun 命令
mpirun --allow-run-as-root --hostfile my_hostfile -np "$np" -N 8 \
 -x GLOO_SOCKET_IFNAME=bond0 \
 -x NCCL_SOCKET_IFNAME=bond0 \
 -x NCCL_DEBUG=INFO \
 -x NCCL_IB_GID_INDEX=3 \
 -x NCCL_IB_QPS_PER_CONNECTION=2 \
#  -x NCCL_PXN_DISABLE=0 \
 -x NCCL_MIN_NCHANNELS=24 \
 -x NCCL_IB_HCA=mlx5_100,mlx5_101,mlx5_102,mlx5_103,mlx5_104,mlx5_105,mlx5_106,mlx5_107 \
 ./build/all_reduce_perf -b 8 -e 2G -f 2 -g 1