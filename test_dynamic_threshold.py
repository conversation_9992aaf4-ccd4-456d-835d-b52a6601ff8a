#!/usr/bin/env python3
"""
测试动态阈值功能的简单脚本
"""

# 从nccl_check_v3.py导入相关配置和函数
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nccl_check_v3 import BANDWIDTH_THRESHOLDS, get_bandwidth_threshold

def test_dynamic_thresholds():
    """测试动态阈值功能"""
    print("=== NCCL动态带宽阈值测试 ===\n")
    
    # 测试不同节点数量的阈值
    test_cases = [1, 2, 3, 4, 5, 8, 10, 16, 20, 32, 50, 100]
    
    print("节点数量 | 动态阈值 | 说明")
    print("-" * 50)
    
    for node_count in test_cases:
        threshold = get_bandwidth_threshold(node_count)
        
        # 确定测试场景
        if node_count <= 2:
            scenario = "两两分组测试"
        elif node_count <= 4:
            scenario = "小组验证测试"
        elif node_count <= 8:
            scenario = "小批量测试"
        elif node_count <= 16:
            scenario = "中等规模测试"
        else:
            scenario = "大规模测试"
            
        print(f"{node_count:^8} | {threshold:^8} | {scenario}")
    
    print("\n=== 阈值配置表 ===")
    print("配置的阈值节点数 | 阈值 (GB/s)")
    print("-" * 30)
    for nodes, threshold in sorted(BANDWIDTH_THRESHOLDS.items()):
        print(f"{nodes:^16} | {threshold:^12}")
    
    print("\n=== 测试场景示例 ===")
    
    # 模拟不同带宽值的判断结果
    test_bandwidths = [90, 80, 70, 60, 50, 40, 30]
    
    for nodes in [2, 4, 8, 16, 32]:
        threshold = get_bandwidth_threshold(nodes)
        print(f"\n{nodes}节点测试 (阈值: {threshold} GB/s):")
        
        for bw in test_bandwidths:
            result = "通过" if bw > threshold else "失败"
            print(f"  带宽 {bw} GB/s -> {result}")

def compare_with_fixed_threshold():
    """对比固定阈值和动态阈值的差异"""
    print("\n\n=== 固定阈值 vs 动态阈值对比 ===\n")
    
    fixed_threshold = 70  # 原来的固定阈值
    test_scenarios = [
        (2, 80, "两两分组测试"),
        (3, 65, "验证测试"),
        (8, 55, "中等规模测试"),
        (16, 45, "大规模测试"),
        (32, 35, "超大规模测试")
    ]
    
    print("场景 | 节点数 | 实际带宽 | 固定阈值结果 | 动态阈值结果 | 动态阈值")
    print("-" * 80)
    
    for nodes, bandwidth, scenario in test_scenarios:
        dynamic_threshold = get_bandwidth_threshold(nodes)
        
        fixed_result = "通过" if bandwidth > fixed_threshold else "失败"
        dynamic_result = "通过" if bandwidth > dynamic_threshold else "失败"
        
        # 标记差异
        diff_marker = " ⚠️" if fixed_result != dynamic_result else ""
        
        print(f"{scenario:<12} | {nodes:^6} | {bandwidth:^8} | {fixed_result:^10} | {dynamic_result:^10} | {dynamic_threshold:^8}{diff_marker}")

if __name__ == "__main__":
    test_dynamic_thresholds()
    compare_with_fixed_threshold()
    
    print("\n=== 总结 ===")
    print("✅ 动态阈值根据节点数量自动调整")
    print("✅ 小规模测试更严格，大规模测试更宽松")
    print("✅ 减少误判，提高检测准确性")
    print("✅ 更符合NCCL实际性能特征")
