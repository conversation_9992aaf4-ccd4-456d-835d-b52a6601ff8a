###
# @Author: wxwang
# @Script description: nccl 慢节点筛选，需要在容器内使用
###
import collections
import os
import random
import subprocess
import sys
import threading
import time
from queue import Queue
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 全局配置
MAX_RETRIES = 2  # 最大重试次数
MAX_DURATION = 18000  # 300分钟超时(秒)
RETRY_DELAY = 5  # 重试间隔(秒)
MAX_THREADS = 100  # 最大并发线程数
MIN_GROUP_SIZE = 4  # 最小分组大小，小于此值不并发
MAX_RECURSION_DEPTH = 6           # 最大递归深度

# 验证策略配置
VALIDATION_CONFIG = {
    'high_error_threshold': 0.7,      # 高错误率阈值，超过此值使用反向验证
    'medium_error_threshold': 0.4,    # 中等错误率阈值，超过此值使用采样验证
    'batch_size': 4,                  # 批量验证的批次大小
    'sample_ratio': 0.3,              # 采样验证的采样比例
    'min_sample_size': 10,            # 最小采样数量
    'reverse_test_count': 3,          # 反向验证时每个witness的测试次数
    'enable_early_termination': True, # 是否启用早期终止
}

# NCCL带宽阈值配置（基于节点数量动态调整）
BANDWIDTH_THRESHOLDS = {
    2: 85,   # 2节点：85 GB/s（两两分组测试，要求较高）
    3: 75,   # 3节点：75 GB/s（验证测试）
    4: 70,   # 4节点：70 GB/s（小批量测试）
    8: 60,   # 8节点：60 GB/s（中等规模）
    16: 50,  # 16节点：50 GB/s（大规模测试）
    32: 40,  # 32节点及以上：40 GB/s（超大规模）
}

# 全局日志文件变量
NCCL_LOG_FILE = ""
APP_LOG_FILE = ""


def ensure_logs_dir():
    """确保logs目录存在"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs") if __file__ != "" else os.path.join(
        os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    return log_dir


def get_log_filenames(node_count):
    """根据节点数量生成日志文件名"""
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = ensure_logs_dir()
    return (
        os.path.join(log_dir, f"nccl_debug_{current_time}_{node_count}n.log"),
        os.path.join(log_dir, f"app_runtime_{current_time}_{node_count}n.log")
    )


def get_timestamp():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def get_bandwidth_threshold(node_count):
    """根据节点数量获取动态带宽阈值"""
    # 找到最接近的配置
    thresholds = sorted(BANDWIDTH_THRESHOLDS.keys())

    if node_count <= thresholds[0]:
        return BANDWIDTH_THRESHOLDS[thresholds[0]]

    for i, threshold_nodes in enumerate(thresholds):
        if node_count <= threshold_nodes:
            return BANDWIDTH_THRESHOLDS[threshold_nodes]

    # 超过最大配置，使用最低阈值
    return BANDWIDTH_THRESHOLDS[thresholds[-1]]


def log(message, level="INFO"):
    global APP_LOG_FILE
    log_entry = f"[{get_timestamp()}] [{level}] {message}"
    print(log_entry)
    if APP_LOG_FILE:
        with open(APP_LOG_FILE, 'a') as f:
            f.write(log_entry + '\n')


def log_nccl_output(output, group_name=""):
    """带组标识的NCCL日志记录"""
    global NCCL_LOG_FILE
    if NCCL_LOG_FILE:
        with open(NCCL_LOG_FILE, 'a') as f:
            f.write(f"\n[{get_timestamp()}] [GROUP {group_name}] NCCL OUTPUT:\n")
            f.write(output + '\n')


def build_host_string(ip_list, slots_per_host=8):
    return ",".join([f"{ip}:{slots_per_host}" for ip in ip_list])


def check_ssh_connection(host, timeout=5):
    """检查SSH免密登录"""
    try:
        cmd = ["ssh", "-o", "BatchMode=yes", "-o", "ConnectTimeout=" + str(timeout),
               host, "echo SSH连接测试成功"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            log(f"SSH连接检查通过: {host}")
            return True
        else:
            log(f"SSH连接失败: {host} - 错误: {result.stderr.strip()}", "ERROR")
            return False
    except subprocess.TimeoutExpired:
        log(f"SSH连接超时: {host} (超过{timeout}秒)", "ERROR")
        return False
    except Exception as e:
        log(f"SSH连接异常: {host} - {str(e)}", "ERROR")
        return False


def check_all_nodes_ssh(nodes, max_threads=50):
    """并发SSH检查"""
    log("\n开始SSH免密登录检查...")
    failed_nodes = []

    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        results = list(executor.map(check_ssh_connection, nodes))

    failed_nodes = [node for node, success in zip(nodes, results) if not success]

    if failed_nodes:
        log(f"\nSSH检查失败节点 ({len(failed_nodes)}个): {failed_nodes}", "ERROR")
        log("请确保以下节点已配置SSH免密登录:", "ERROR")
        for node in failed_nodes:
            log(f"  ssh-copy-id {node}", "ERROR")
        return False
    return True


def allreduce_test(node_group, group_name=""):
    """执行NCCL测试并返回结果"""
    slots_per_host = 8
    host_string = build_host_string(node_group, slots_per_host)
    total_processes = len(node_group) * slots_per_host
    test_timeout = 80  # 测试超时时间(秒)

    test_id = f"{group_name}-{time.strftime('%m%d%H%M%S')}-{random.randint(1000, 9999)}"

    # 记录测试开始信息
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"\n\n{'=' * 80}\n")
        f.write(f"[TEST START] Group: {group_name} | ID: {test_id}\n")
        f.write(f"Nodes: {node_group}\n")
        f.write(f"Processes: {total_processes}\n")
        f.write(f"Start Time: {get_timestamp()}\n")
        f.write(f"{'=' * 80}\n")

    cmd = [
        "mpirun",
        "--allow-run-as-root",
        "-np", str(total_processes),
        "-host", host_string,
        "-x", "NCCL_DEBUG=INFO",
        "-x", "NCCL_IB_HCA=mlx5_100,mlx5_101,mlx5_102,mlx5_103,mlx5_104,mlx5_105,mlx5_106,mlx5_107",
        "-x", "NCCL_ALGO=Ring",
        "-x", "NCCL_IB_QPS_PER_CONNECTION=2",
        # "-x", "NCCL_PXN_DISABLE=0",
        "-x", "NCCL_MIN_NCHANNELS=24",
        "-x", "NCCL_SOCKET_IFNAME=bond0",
        "-x", "NCCL_IB_GID_INDEX=3",
        "-x", f"NCCL_TIMEOUT=75",
        "./build/all_reduce_perf",
        "-b", "16G",
        "-e", "16G",
        "-g", "1",
        "-f", "2",
        "-T", "75"
    ]

    log(f"\n[测试 {group_name} | ID: {test_id}] 启动测试组")
    log(f"[测试 {group_name} | ID: {test_id}]   节点列表: {node_group}")
    log(f"[测试 {group_name} | ID: {test_id}]   总进程数: {total_processes}  (机器数: {len(node_group)})")
    log(f"[测试 {group_name} | ID: {test_id}]   完整命令: {' '.join(cmd)}")

    attempt = 0
    while attempt < MAX_RETRIES:
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=test_timeout)
            elapsed = time.time() - start_time

            # 记录详细结果
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST RESULT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Exit Code: {result.returncode}\n")
                f.write(f"Duration: {elapsed:.2f}s\n")
                f.write("=== STDOUT ===\n")
                f.write(result.stdout + "\n")
                if result.stderr:
                    f.write("=== STDERR ===\n")
                    f.write(result.stderr + "\n")

            # 解析带宽
            bandwidth = None
            for line in result.stdout.split('\n'):
                if "Avg bus bandwidth" in line:
                    try:
                        bandwidth = float(line.split()[-1])
                        break
                    except:
                        continue

            # 使用动态阈值
            threshold = get_bandwidth_threshold(len(node_group))
            success = bandwidth is not None and bandwidth > threshold
            status = "通过" if success else "失败"
            log(f"[测试 {group_name} | ID: {test_id}] 测试结果: {status}, 带宽: {bandwidth or '无'} GB/s (阈值: {threshold} GB/s), 耗时: {elapsed:.2f}s")

            # 记录测试结束
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST END] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Status: {'SUCCESS' if success else 'FAILED'}\n")
                f.write(f"End Time: {get_timestamp()}\n")
                f.write(f"{'=' * 80}\n")

            return success

        except subprocess.TimeoutExpired as e:
            attempt += 1
            elapsed = time.time() - start_time
            # 等待前期测试的显存回收
            time.sleep(10)
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次超时 (耗时{elapsed:.2f}s/{test_timeout}s)",
                "WARNING")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST TIMEOUT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Elapsed: {elapsed:.2f}s\n")
                f.write(f"Error: {str(e)}\n")
            time.sleep(RETRY_DELAY)

        except Exception as e:
            attempt += 1
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次异常: {str(e)}", "ERROR")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST ERROR] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Error Type: {type(e).__name__}\n")
                f.write(f"Error Msg: {str(e)}\n")
            time.sleep(RETRY_DELAY)

    # 所有重试均失败
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"\n[TEST FAILED] Group: {group_name} | ID: {test_id}\n")
        f.write(f"All {MAX_RETRIES} attempts failed\n")
        f.write(f"{'=' * 80}\n")
    log(f"[测试 {group_name} | ID: {test_id}] 所有重试均失败", "ERROR")
    return False


def shuffled_diff(previous_groups, current_nodes, witnesses=None):
    """确保新分组与历史分组不重复，并排除已确认正常的节点"""
    witnesses = witnesses or []
    # 过滤掉已确认正常的节点
    valid_nodes = [node for node in current_nodes if node not in witnesses]

    # 将历史分组转换为冻结集合的集合（顺序无关）
    previous_group_sets = {frozenset(group) for group in previous_groups}

    max_attempts = 10  # 防止无限循环
    for _ in range(max_attempts):
        random.shuffle(valid_nodes)
        # 生成当前分组方案（每组2节点）
        current_groups = [
            valid_nodes[i:i + 2]
            for i in range(0, len(valid_nodes), 2)
        ]

        # 检查是否与历史分组重复
        current_group_sets = {frozenset(group) for group in current_groups}
        if not current_group_sets & previous_group_sets:
            return valid_nodes

    return valid_nodes  # 如果所有尝试都失败，返回原始顺序


def prescreen(nodes, group_name="主组", witnesses=None, depth=0):
    """
    完整版NCCL慢节点筛查逻辑
    优化点：
    1. 3节点专用处理流程
    2. 两两分组失败时自动重试
    3. 单通过组时跳过合并测试
    4. 完善的日志记录
    """
    # 初始化
    witnesses = witnesses or []
    nodes = [n for n in nodes if n not in witnesses]
    suspects = nodes.copy()

    # ===== 3节点特殊处理 =====
    if len(nodes) == 3:
        return handle_three_nodes(nodes, group_name, witnesses, depth)

    # 保留组名称处理
    current_group = f"{group_name}-D{depth}" if depth > 0 else group_name

    log(f"\n{'=' * 60}")
    log(f"[筛查 {current_group}] 启动筛查 | 节点数: {len(nodes)}")
    log(f"{'=' * 60}")

    # === 第1步：完整组测试 ===
    log(f"\n[阶段1] 完整组测试 ({len(suspects)}节点)")
    if allreduce_test(suspects, f"{current_group}-完整组"):
        witnesses.extend(suspects)
        return [], witnesses

    # 等待前期测试的显存回收
    time.sleep(5)

    # === 第2步：两两分组测试（含重试）===
    passed_groups, failed_nodes = run_paired_testing(suspects, current_group)


    # === 第3步：合并验证（智能跳过单组）===
    passed_nodes = [node for group in passed_groups for node in group]

    if not passed_groups:
        log("所有两两分组均失败，无法缩小问题范围", "ERROR")
        return suspects, witnesses

    elif len(passed_groups) == 1:  # 优化点：单通过组直接确认
        log(f"仅1个小组通过，直接确认节点正常: {passed_nodes}")
        witnesses.extend(passed_nodes)
        suspects = failed_nodes

    else:  # 多通过组需要合并验证
        log(f"{'=' * 60}")
        log(f"\n[阶段3] 合并验证 ({len(passed_nodes)}节点)")
        # 等待前期测试的显存回收
        time.sleep(10)
        if allreduce_test(passed_nodes, f"{current_group}-合并组"):
            witnesses.extend(passed_nodes)
            suspects = failed_nodes
        else:
            log("合并验证失败，开始二分验证", "WARNING")
            time.sleep(10)
            suspects = binary_verify(passed_nodes, failed_nodes, current_group, witnesses, depth)

    # === 第4步：最终处理 ===
    if len(suspects) == 2:
        log(f"\n[阶段4] 最终2节点测试: {suspects}")
        if allreduce_test(suspects, f"{current_group}-最终2节点"):
            witnesses.extend(suspects)
            suspects = []

    # 返回前确保去重
    witnesses = list(set(witnesses))
    suspects = list(set(suspects) - set(witnesses))

    log(f"\n{'=' * 60}")
    log(f"[筛查 {current_group}] 筛查完成")
    log(f"正常节点: {len(witnesses)}个 | 可疑节点: {len(suspects)}个")
    log(f"{'=' * 60}")

    return suspects, witnesses


def run_paired_testing(nodes, group_name, max_retries=2):
    """执行两两分组测试（确保每次重试分组不同）"""
    passed_groups = []
    failed_nodes = []
    previous_groupings = []  # 记录历史分组方案

    for retry in range(max_retries + 1):
        # 生成全新分组方案（确保与之前不同）
        new_nodes = generate_unique_grouping(nodes, previous_groupings)
        subgroups = [new_nodes[i:i + 2] for i in range(0, len(new_nodes), 2)]
        previous_groupings.append(subgroups)  # 记录本次分组

        # 并发测试
        with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            futures = {
                executor.submit(allreduce_test, group, f"{group_name}-小组-{i}-重试{retry}"): group
                for i, group in enumerate(subgroups)
            }
            for future in futures:
                group = futures[future]
                try:
                    if future.result():
                        passed_groups.append(group)
                    else:
                        failed_nodes.extend(group)
                except Exception as e:
                    log(f"小组测试异常: {str(e)}", "ERROR")
                    failed_nodes.extend(group)

        if passed_groups or retry == max_retries:
            break

        log(f"第{retry + 1}次重试将使用全新分组方案", "INFO")

    return passed_groups, failed_nodes


def generate_unique_grouping(nodes, previous_groupings):
    """生成与历史分组不同的新方案"""
    max_attempts = 10
    for _ in range(max_attempts):
        # 方法1：随机打乱+验证
        shuffled = nodes.copy()
        random.shuffle(shuffled)
        new_groups = [shuffled[i:i + 2] for i in range(0, len(shuffled), 2)]

        # 检查是否与历史分组重复
        is_unique = True
        for past in previous_groupings:
            if collections.Counter(frozenset(pair) for pair in past) == \
                    collections.Counter(frozenset(pair) for pair in new_groups):
                is_unique = False
                break

        if is_unique:
            return shuffled

    # 方法2：强制不同（当随机无法满足时）
    log("随机打乱无法获得新分组，使用轮换法", "WARNING")
    return nodes[1:] + [nodes[0]]


def handle_three_nodes(nodes, group_name, witnesses=None, depth=0):
    """
    完整版3节点处理逻辑
    功能：
    1. 先进行完整组测试
    2. 完整组失败后穷举所有2节点组合测试
    3. 严格验证通过组
    4. 完善的日志记录
    参数：
        nodes: 待测的3个节点IP列表，如 ["*********", "*********", "*********"]
        group_name: 测试组标识
        witnesses: 已知正常节点列表
        depth: 递归深度
    返回：
        (可疑节点列表, 确认正常节点列表)
    """
    if len(nodes) != 3:
        raise ValueError("handle_three_nodes只接受3个节点")

    current_group = f"{group_name}-3节点-D{depth}" if depth > 0 else f"{group_name}-3节点"
    node_a, node_b, node_c = nodes  # 解构节点IP

    log(f"\n{'=' * 60}")
    log(f"[筛查 {current_group}] 启动3节点深度检测")
    log(f"待测节点: {nodes}")
    log(f"{'=' * 60}")

    # 首先进行完整组测试
    log(f"\n[测试] 完整3节点组测试")
    if allreduce_test(nodes, f"{current_group}-完整组"):
        log("完整组测试通过，所有节点正常")
        return [], witnesses + nodes if witnesses else nodes

    # 完整组测试失败，继续两两组合测试
    log("完整组测试失败，开始两两组合测试", "WARNING")

    # 测试所有2节点组合（AB, AC, BC）
    test_cases = [
        ([node_a, node_b], f"{current_group}-{node_a}_{node_b}"),
        ([node_a, node_c], f"{current_group}-{node_a}_{node_c}"),
        ([node_b, node_c], f"{current_group}-{node_b}_{node_c}")
    ]

    # 执行所有测试（带超时处理）
    results = {}
    for nodes_pair, test_name in test_cases:
        log(f"\n[测试] 组合 {nodes_pair}")
        try:
            results[tuple(nodes_pair)] = allreduce_test(nodes_pair, test_name)
        except Exception as e:
            log(f"组合测试异常: {str(e)}", "ERROR")
            results[tuple(nodes_pair)] = False

    # 结果分析
    passed_pairs = [pair for pair, passed in results.items() if passed]
    failed_pairs = [pair for pair, passed in results.items() if not passed]

    # 情况1：全部组合失败 → 可能多个坏节点
    if not passed_pairs:
        log("所有2节点组合均失败，可能多个坏节点或网络问题", "WARNING")
        return nodes, []  # 全部标记为可疑

    # 情况2：只有1个组合通过 → 需要验证
    elif len(passed_pairs) == 1:
        good_nodes = list(passed_pairs[0])
        bad_node = list(set(nodes) - set(good_nodes))[0]

        # 关键验证：确保通过组真实有效
        log(f"\n[验证] 确认正常组: {good_nodes}")
        if allreduce_test(good_nodes, f"{current_group}-最终验证"):
            log(f"确认异常节点: {bad_node} | 正常节点: {good_nodes}")
            return [bad_node], good_nodes
        else:
            log("正常组验证失败，所有节点可疑", "WARNING")
            return nodes, []

    # 情况3：多个组合通过 → 矛盾结果
    else:
        log("矛盾结果：多个组合通过但完整组失败", "ERROR")
        return nodes, []  # 保守返回全部可疑


def binary_verify(nodes, existing_failed, group_name, witnesses, depth):
    """并发二分验证（核心改进点）"""
    if depth >= MAX_RECURSION_DEPTH:
        log(f"达到最大递归深度{depth}，终止二分", "WARNING")
        return nodes + existing_failed

    while len(nodes) > 2:
        # 等待前期测试的显存回收
        time.sleep(10)
        mid = len(nodes) // 2
        left, right = nodes[:mid], nodes[mid:]

        log(f"\n[二分验证 {group_name}] 并发测试子组 | "
            f"左组: {left} | 右组: {right}")

        # 并发测试（带超时控制）
        left_status = right_status = None
        with ThreadPoolExecutor(max_workers=2) as executor:
            left_future = executor.submit(allreduce_test, left, f"{group_name}-二分左-D{depth}")
            right_future = executor.submit(allreduce_test, right, f"{group_name}-二分右-D{depth}")
            
            # 分别处理每个子组的结果
            try:
                left_result = left_future.result()
                if left_result:
                    witnesses.extend(left)
                    log(f"左子组验证通过: {left}", "INFO")
                    left_status = "passed"
                else:
                    left_status = "failed"
            except Exception as e:
                log(f"左子组测试异常: {str(e)}", "WARNING")
                left_status = "failed"

            try:
                right_result = right_future.result()
                if right_result:
                    witnesses.extend(right)
                    log(f"右子组验证通过: {right}", "INFO")
                    right_status = "passed"
                else:
                    right_status = "failed"
            except Exception as e:
                log(f"右子组测试异常: {str(e)}", "WARNING")
                right_status = "failed"

        # 决策逻辑（核心改进）
        if left_status == "passed" and right_status == "passed":
            return existing_failed
        elif left_status == "passed":
            nodes = right  # 右组有问题
        elif right_status == "passed":
            nodes = left   # 左组有问题
        else:
            # 两侧都有问题，递归处理
            log("两侧子组均异常，递归处理", "INFO")
            left_suspects = binary_verify(left, [], group_name, witnesses, depth+1)
            right_suspects = binary_verify(right, [], group_name, witnesses, depth+1)
            return left_suspects + right_suspects + existing_failed

    return nodes + existing_failed


def cross_validate_two_nodes(nodes, existing_failed, group_name, witnesses):
    """对最后2节点进行交叉验证"""
    node_a, node_b = nodes

    # 验证节点A（与已知正常节点组队）
    test_group_a = random.sample(witnesses, min(2, len(witnesses))) + [node_a]
    a_ok = allreduce_test(test_group_a, f"{group_name}-交叉验证-A")

    # 验证节点B（与已知正常节点组队）
    test_group_b = random.sample(witnesses, min(2, len(witnesses))) + [node_b]
    b_ok = allreduce_test(test_group_b, f"{group_name}-交叉验证-B")

    # 结果判定
    if a_ok and not b_ok:
        return [node_b], witnesses + [node_a]
    elif b_ok and not a_ok:
        return [node_a], witnesses + [node_b]
    else:
        return nodes + existing_failed, witnesses  # 都失败或都通过


def validate_suspects(suspects, witnesses):
    """智能验证可疑节点，针对大量错误节点场景优化"""
    validation_start_time = time.time()

    # 首先确保输入列表不重叠
    suspects = [node for node in suspects if node not in witnesses]
    witnesses = [node for node in witnesses if node not in suspects]

    if not suspects:
        log("[验证阶段] 没有可疑节点需要验证", "INFO")
        return [], witnesses

    if len(witnesses) < 2:
        log("[验证阶段] 正常节点不足，无法验证可疑节点", "WARNING")
        return suspects, witnesses

    # 计算错误节点比例
    total_nodes = len(suspects) + len(witnesses)
    error_ratio = len(suspects) / total_nodes

    log(f"\n[验证阶段] ============ 智能验证策略分析 ============")
    log(f"可疑节点: {len(suspects)}个, 正常节点: {len(witnesses)}个")
    log(f"错误节点比例: {error_ratio:.2%}")

    # 估算传统方法所需时间
    traditional_time_estimate = len(suspects) * 80  # 每个节点80秒
    log(f"传统逐个验证预估时间: {traditional_time_estimate/60:.1f}分钟")

    # 策略选择基于配置
    if error_ratio > VALIDATION_CONFIG['high_error_threshold']:
        log(f"[验证阶段] 错误节点比例过高({error_ratio:.2%} > {VALIDATION_CONFIG['high_error_threshold']:.0%})，采用反向验证策略")
        result = reverse_validate_witnesses(suspects, witnesses)

    elif error_ratio > VALIDATION_CONFIG['medium_error_threshold']:
        log(f"[验证阶段] 中等错误率({error_ratio:.2%})，采用采样验证策略")
        result = sampling_validate(suspects, witnesses)

    elif len(suspects) > len(witnesses) * 3:
        log("[验证阶段] 可疑节点数量较多，采用批量验证策略")
        result = batch_validate(suspects, witnesses)

    else:
        log("[验证阶段] 使用优化的并发验证策略")
        result = optimized_concurrent_validate(suspects, witnesses)

    # 性能统计
    actual_time = time.time() - validation_start_time
    time_saved = traditional_time_estimate - actual_time
    efficiency_gain = (time_saved / traditional_time_estimate) * 100 if traditional_time_estimate > 0 else 0

    log(f"\n[验证阶段] ============ 性能统计 ============")
    log(f"实际验证耗时: {actual_time/60:.1f}分钟")
    log(f"节省时间: {time_saved/60:.1f}分钟")
    log(f"效率提升: {efficiency_gain:.1f}%")

    return result


def reverse_validate_witnesses(suspects, witnesses):
    """反向验证：验证witnesses的可靠性，而不是逐个验证suspects"""
    log(f"[反向验证] 验证 {len(witnesses)} 个正常节点的可靠性")

    # 随机选择一些suspects作为测试对象
    test_suspects = random.sample(suspects, min(10, len(suspects)))
    reliable_witnesses = []

    for witness in witnesses:
        # 用witness与多个suspect组合测试
        success_count = 0
        test_count = min(3, len(test_suspects))  # 最多测试3次

        for i in range(test_count):
            test_suspect = test_suspects[i % len(test_suspects)]
            test_group = [witness, test_suspect]

            if allreduce_test(test_group, f"反向验证-{witness}-{i}"):
                success_count += 1

        # 如果witness在大部分测试中都能工作，认为它是可靠的
        if success_count >= test_count // 2:
            reliable_witnesses.append(witness)
            log(f"[反向验证] 节点 {witness} 验证可靠 ({success_count}/{test_count})")
        else:
            log(f"[反向验证] 节点 {witness} 可能有问题 ({success_count}/{test_count})", "WARNING")

    # 基于可靠witnesses的数量决定策略
    if len(reliable_witnesses) >= 2:
        log(f"[反向验证] 发现 {len(reliable_witnesses)} 个可靠节点，其余节点标记为异常")
        return suspects + [w for w in witnesses if w not in reliable_witnesses], reliable_witnesses
    else:
        log("[反向验证] 可靠节点不足，保守返回原结果", "WARNING")
        return suspects, witnesses


def sampling_validate(suspects, witnesses):
    """采样验证：只验证部分suspects，根据结果推断整体"""
    sample_ratio = VALIDATION_CONFIG['sample_ratio']
    min_sample = VALIDATION_CONFIG['min_sample_size']
    sample_size = max(min_sample, int(len(suspects) * sample_ratio))
    sample_suspects = random.sample(suspects, min(sample_size, len(suspects)))

    log(f"[采样验证] 从 {len(suspects)} 个可疑节点中采样 {len(sample_suspects)} 个进行验证")

    # 对采样节点进行快速验证
    confirmed_good = []
    confirmed_bad = []

    for suspect in sample_suspects:
        # 选择2个witnesses进行测试
        test_witnesses = random.sample(witnesses, min(2, len(witnesses)))
        test_group = test_witnesses + [suspect]

        if allreduce_test(test_group, f"采样验证-{suspect}"):
            confirmed_good.append(suspect)
        else:
            confirmed_bad.append(suspect)

    # 根据采样结果推断
    if len(confirmed_good) == 0:
        log("[采样验证] 采样节点全部异常，推断所有可疑节点均异常")
        return suspects, witnesses
    elif len(confirmed_bad) == 0:
        log("[采样验证] 采样节点全部正常，推断所有可疑节点均正常")
        return [], witnesses + suspects
    else:
        # 混合结果：对剩余节点进行批量验证
        remaining = [s for s in suspects if s not in sample_suspects]
        log(f"[采样验证] 混合结果，对剩余 {len(remaining)} 个节点进行批量验证")
        remaining_bad, remaining_good = batch_validate(remaining, witnesses + confirmed_good)
        return confirmed_bad + remaining_bad, witnesses + confirmed_good + remaining_good


def batch_validate(suspects, witnesses):
    """批量验证：将suspects分批测试，提高效率"""
    batch_size = VALIDATION_CONFIG['batch_size']
    log(f"[批量验证] 将 {len(suspects)} 个可疑节点分批验证，批次大小: {batch_size}")

    confirmed_good = []
    confirmed_bad = []

    # 将suspects分批
    for i in range(0, len(suspects), batch_size):
        batch = suspects[i:i + batch_size]

        # 选择足够的witnesses与批次组合
        test_witnesses = random.sample(witnesses, min(2, len(witnesses)))
        test_group = test_witnesses + batch

        log(f"[批量验证] 测试批次 {i//batch_size + 1}: {batch}")

        if allreduce_test(test_group, f"批量验证-批次{i//batch_size + 1}"):
            # 整个批次通过，所有节点都是好的
            confirmed_good.extend(batch)
            log(f"[批量验证] 批次通过，确认正常: {batch}")
        else:
            # 批次失败，需要逐个验证
            log(f"[批量验证] 批次失败，逐个验证: {batch}")
            for suspect in batch:
                test_group = test_witnesses + [suspect]
                if allreduce_test(test_group, f"批量验证-单个-{suspect}"):
                    confirmed_good.append(suspect)
                else:
                    confirmed_bad.append(suspect)

    return confirmed_bad, witnesses + confirmed_good


def optimized_concurrent_validate(suspects, witnesses):
    """优化的并发验证：提高witnesses利用率"""
    log(f"[并发验证] 优化并发验证 {len(suspects)} 个可疑节点")

    # 使用队列管理
    suspect_queue = Queue()
    for suspect in suspects:
        suspect_queue.put(suspect)

    # 结果存储
    results = []
    results_lock = threading.Lock()

    # 优化：允许witnesses重复使用，但限制同时使用数量
    witnesses_semaphore = threading.Semaphore(len(witnesses))

    def worker():
        while not suspect_queue.empty():
            try:
                suspect = suspect_queue.get_nowait()

                # 获取witnesses使用权限
                witnesses_semaphore.acquire()
                try:
                    # 选择witnesses
                    selected = random.sample(witnesses, min(2, len(witnesses)))
                    test_group = selected + [suspect]
                    success = allreduce_test(test_group, f"并发验证-{suspect}")

                    with results_lock:
                        results.append((suspect, success))
                        log(f"[并发验证] 节点 {suspect} 验证结果: {'通过' if success else '失败'}")

                finally:
                    witnesses_semaphore.release()

            except Exception as e:
                log(f"[并发验证] 验证节点异常: {str(e)}", "ERROR")
                witnesses_semaphore.release()

    # 启动线程池 - 优化并发度计算
    max_workers = min(MAX_THREADS, len(suspects), len(witnesses) * 2)  # 允许更高并发
    threads = []
    for _ in range(max_workers):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)

    # 等待完成
    for t in threads:
        t.join()

    # 处理结果
    confirmed_good = []
    confirmed_bad = []
    for suspect, success in results:
        if success:
            confirmed_good.append(suspect)
        else:
            confirmed_bad.append(suspect)

    return confirmed_bad, witnesses + confirmed_good


def main():
    global NCCL_LOG_FILE, APP_LOG_FILE

    if len(sys.argv) != 2:
        print("使用方法: python allreduce_checker.py <节点列表文件路径>")
        sys.exit(1)

    # 读取节点列表
    try:
        with open(sys.argv[1], 'r') as file:
            nodes = [line.strip() for line in file if line.strip()]
    except Exception as e:
        print(f"无法读取节点文件: {str(e)}")
        sys.exit(1)

    if not nodes:
        print("错误: 节点列表为空")
        sys.exit(1)

    # 设置日志文件
    NCCL_LOG_FILE, APP_LOG_FILE = get_log_filenames(len(nodes))

    with open(NCCL_LOG_FILE, 'w') as f:
        f.write(f"NCCL调试日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")
    with open(APP_LOG_FILE, 'w') as f:
        f.write(f"应用运行日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")

    log(f"从文件成功加载 {len(nodes)} 个节点")
    start_time = time.time()

    # 检查SSH连通性
    if not check_all_nodes_ssh(nodes):
        log("SSH免密登录检查未通过，程序终止", "ERROR")
        sys.exit(1)

    # 主筛查流程
    suspects, witnesses = prescreen(nodes)
    suspects, witnesses = validate_suspects(suspects, witnesses)

    # 结果汇总
    log(f"\n============ 测试总结 ============")
    log(f"总耗时: {time.time() - start_time:.2f}秒")
    log("\n============ 节点状态汇总 ============")
    log(f"正常节点 ({len(witnesses)}个): {sorted(witnesses)}")
    log(f"异常节点 ({len(suspects)}个): {sorted(suspects)}", "ERROR" if suspects else "INFO")

    log("\n============ 日志文件 ============")
    log(f"NCCL调试日志已保存至: {NCCL_LOG_FILE}")
    log(f"应用运行日志已保存至: {APP_LOG_FILE}")


if __name__ == "__main__":
    main()