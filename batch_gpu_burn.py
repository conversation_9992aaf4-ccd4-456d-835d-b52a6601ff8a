#!/usr/bin/env python3
"""
GPU集群并发测试工具（完整日志版）
修改内容：
1. 汇总日志文件名包含易读时间和主机数量
2. 时间格式示例：2023-11-15_14-30-45
3. 文件名示例：summary_2023-11-15_14-30-45_4nodes.log
4. 增加颜色输出，区分正常和错误信息
5. 优化结果汇总格式，提高可读性
"""

import argparse
import concurrent.futures
import paramiko
import time
import sys
from pathlib import Path
from time import time as current_time
from datetime import datetime

# 配置
LOG_DIR = Path("remote_gpu_logs")
DEFAULT_SCRIPT = "/devops/gpu-burn-master/gpu_power_check.py"
SSH_PORT = 60023
SSH_TIMEOUT = 30
CMD_TIMEOUT = 3600
READ_INTERVAL = 0.1

# 颜色代码
RED = '\033[91m'
GREEN = '\033[92m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
BOLD = '\033[1m'
RESET = '\033[0m'


class Logger:
    """日志记录器，同时输出到控制台和文件"""

    def __init__(self, log_path):
        LOG_DIR.mkdir(exist_ok=True)
        self.console = sys.stdout
        self.log_file = open(log_path, 'a')
        self.use_color = sys.stdout.isatty()  # 检查是否在终端中运行

    def write(self, message):
        # 写入控制台时保留颜色，写入文件时去除颜色代码
        self.console.write(message)
        
        # 移除ANSI颜色代码后写入文件
        clean_message = self._strip_color(message)
        self.log_file.write(clean_message)
        self.log_file.flush()

    def _strip_color(self, text):
        """移除ANSI颜色代码"""
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        return ansi_escape.sub('', text)

    def flush(self):
        self.console.flush()
        self.log_file.flush()


def get_summary_log_name(host_count):
    """生成带时间和主机数的日志文件名"""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    return LOG_DIR / f"summary_{timestamp}_{host_count}nodes.log"


def parse_hosts(hosts_file):
    """解析主机文件"""
    with open(hosts_file) as f:
        hosts = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        return hosts, len(hosts)  # 返回主机列表和数量


def log_with_ip(ip, message, log_file=None, is_error=False):
    """带IP地址的日志记录，增加颜色区分"""
    color = RED if is_error else BLUE
    status = "ERROR" if is_error else "INFO"
    prefix = f"{color}[{ip}-{status}]{RESET}"
    full_msg = f"{prefix} {message.strip()}"
    print(full_msg)  # 这会自动写入汇总日志
    if log_file:
        with open(log_file, 'a') as f:
            # 写入文件时不包含颜色代码
            clean_msg = f"[{ip}-{status}] {message.strip()}"
            f.write(clean_msg + "\n")
    return full_msg


def run_remote_check(host, duration, remote_script, total_hosts, host_index):
    """执行远程GPU检查，增加进度显示"""
    log_file = LOG_DIR / f"{host}.log"
    progress = f"[{host_index}/{total_hosts}]"
    
    with open(log_file, 'w') as f:
        f.write(f"=== 开始测试 {host} {progress} ===\n")
        f.write(f"远程脚本: {remote_script}\n")
        f.write(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试时长: {duration}秒\n\n")

    # 记录开始测试
    print(f"{GREEN}{BOLD}[开始测试] {host} {progress}{RESET}")
    
    client = None
    start_time = current_time()
    try:
        # SSH连接
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        log_with_ip(host, f"正在连接...", log_file)
        client.connect(
            hostname=host,
            port=SSH_PORT,
            username="root",
            timeout=SSH_TIMEOUT,
            banner_timeout=20
        )

        # 执行测试命令
        cmd = f"python {remote_script} -t {duration}"
        log_with_ip(host, f"执行命令: {cmd}", log_file)
        stdin, stdout, stderr = client.exec_command(cmd, get_pty=True, timeout=CMD_TIMEOUT)
        channel = stdout.channel
        channel.setblocking(0)

        # 实时读取输出
        while not channel.exit_status_ready():
            if current_time() - start_time > CMD_TIMEOUT:
                raise TimeoutError("命令执行超时")

            # 处理标准输出
            while channel.recv_ready():
                data = channel.recv(1024).decode()
                for line in data.splitlines():
                    if line.strip():  # 忽略空行
                        log_with_ip(host, line, log_file)

            # 处理错误输出
            while channel.recv_stderr_ready():
                err_data = channel.recv_stderr(1024).decode()
                for line in err_data.splitlines():
                    if line.strip():  # 忽略空行
                        log_with_ip(host, line, log_file, is_error=True)

            time.sleep(READ_INTERVAL)

        exit_status = channel.recv_exit_status()
        elapsed = current_time() - start_time
        
        # 记录测试结果
        status_str = f"{GREEN}成功{RESET}" if exit_status == 0 else f"{RED}失败{RESET}"
        log_with_ip(host, f"测试完成，状态: {status_str}，耗时: {elapsed:.1f}秒", log_file, is_error=(exit_status != 0))
        
        return (host, exit_status == 0, elapsed)

    except Exception as e:
        elapsed = current_time() - start_time
        error_msg = f"测试异常: {str(e)}，耗时: {elapsed:.1f}秒"
        log_with_ip(host, error_msg, log_file, is_error=True)
        return (host, False, elapsed)
    finally:
        if client:
            client.close()


def main():
    parser = argparse.ArgumentParser(description='GPU集群测试工具')
    parser.add_argument('hosts_file', help='主机列表文件')
    parser.add_argument('-t', '--time', type=int, default=300,
                        help='测试时长(秒)，默认300')
    parser.add_argument('-j', '--jobs', type=int, default=10,
                        help='最大并发数，默认10')
    parser.add_argument('-s', '--script', default=DEFAULT_SCRIPT,
                        help=f'远程脚本路径，默认: {DEFAULT_SCRIPT}')
    args = parser.parse_args()

    # 获取主机信息
    hosts, host_count = parse_hosts(args.hosts_file)

    # 初始化日志（带时间戳和主机数）
    summary_log = get_summary_log_name(host_count)
    sys.stdout = Logger(summary_log)

    # 记录开始信息
    print(f"\n{BOLD}=== 开始GPU集群测试 ==={RESET}")
    print(f"{YELLOW}主机数量: {host_count} | 并发数: {args.jobs}{RESET}")
    print(f"{YELLOW}测试时长: {args.time}秒 | 远程脚本: {args.script}{RESET}")
    print(f"{YELLOW}日志目录: {LOG_DIR}{RESET}")
    print(f"{YELLOW}汇总日志: {summary_log}{RESET}\n")

    # 并发测试
    results = []
    start_time = current_time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=args.jobs) as executor:
        futures = {
            executor.submit(run_remote_check, host, args.time, args.script, host_count, i+1): host
            for i, host in enumerate(hosts)
        }

        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    # 计算总耗时
    total_elapsed = current_time() - start_time

    # 汇总报告
    print(f"\n{BOLD}=== 测试结果汇总 ==={RESET}")
    
    # 通过的主机
    passed_hosts = [r for r in results if r[1]]
    print(f"\n{GREEN}{BOLD}[通过的主机] {len(passed_hosts)}/{host_count}{RESET}")
    for host, success, elapsed in passed_hosts:
        print(f"  {GREEN}✓ {host} - {elapsed:.1f}秒{RESET}")

    # 失败的主机
    failed_hosts = [r for r in results if not r[1]]
    if failed_hosts:
        print(f"\n{RED}{BOLD}[失败的主机] {len(failed_hosts)}/{host_count}{RESET}")
        for host, success, elapsed in failed_hosts:
            print(f"  {RED}✗ {host} - {elapsed:.1f}秒{RESET}")
            # 显示失败主机的日志文件路径，方便查看详细错误
            log_path = LOG_DIR / f"{host}.log"
            print(f"    {YELLOW}详细日志: {log_path}{RESET}")
    
    # 统计信息
    pass_rate = len(passed_hosts) / host_count * 100 if host_count > 0 else 0
    print(f"\n{BOLD}测试完成 | 通过率: {pass_rate:.1f}% ({len(passed_hosts)}/{host_count}) | 总耗时: {total_elapsed:.1f}秒{RESET}")
    print(f"{YELLOW}详细日志目录: {LOG_DIR}{RESET}")

    # 恢复标准输出
    sys.stdout.log_file.close()
    sys.stdout = sys.stdout.console

    # 打印最终日志路径（恢复stdout后）
    print(f"\n{GREEN}测试完成，汇总日志已保存至: {summary_log}{RESET}")
    
    # 如果有失败的主机，返回非零退出码
    if failed_hosts:
        print(f"\n{RED}警告: {len(failed_hosts)}台主机测试失败，请检查详细日志{RESET}")
        sys.exit(1)


if __name__ == "__main__":
    main()