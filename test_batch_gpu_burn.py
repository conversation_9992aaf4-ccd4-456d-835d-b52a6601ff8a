#!/usr/bin/env python3
"""
batch_gpu_burn.py的pytest测试
测试内容：
1. 日志记录功能
2. 主机解析功能
3. 命令行参数解析
4. SSH连接模拟
"""

import os
import sys
import time
import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open

# 导入被测模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import batch_gpu_burn as bgb


class TestLogger:
    """测试Logger类"""
    
    def test_logger_init(self):
        """测试Logger初始化"""
        with tempfile.NamedTemporaryFile() as tmp:
            logger = bgb.Logger(tmp.name)
            assert logger.console == sys.stdout
            assert logger.log_file.name == tmp.name
    
    def test_logger_write(self):
        """测试Logger写入功能"""
        with tempfile.NamedTemporaryFile() as tmp:
            with patch('sys.stdout') as mock_stdout:
                logger = bgb.Logger(tmp.name)
                logger.write("测试消息")
                
                # 检查控制台输出
                mock_stdout.write.assert_called_with("测试消息")
                
                # 检查文件输出
                tmp.seek(0)
                content = tmp.read().decode()
                assert "测试消息" in content
    
    def test_strip_color(self):
        """测试颜色代码移除功能"""
        logger = bgb.Logger("dummy.log")
        colored_text = f"{bgb.RED}错误{bgb.RESET}"
        clean_text = logger._strip_color(colored_text)
        assert clean_text == "错误"
        assert bgb.RED not in clean_text
        assert bgb.RESET not in clean_text


class TestHostParsing:
    """测试主机解析功能"""
    
    def test_parse_hosts_normal(self):
        """测试正常主机文件解析"""
        hosts_content = "host1\nhost2\nhost3\n"
        
        with patch("builtins.open", mock_open(read_data=hosts_content)):
            hosts, count = bgb.parse_hosts("dummy.txt")
            assert hosts == ["host1", "host2", "host3"]
            assert count == 3
    
    def test_parse_hosts_with_comments(self):
        """测试带注释的主机文件解析"""
        hosts_content = "host1\n# 注释行\nhost2\n\nhost3"
        
        with patch("builtins.open", mock_open(read_data=hosts_content)):
            hosts, count = bgb.parse_hosts("dummy.txt")
            assert hosts == ["host1", "host2", "host3"]
            assert count == 3
    
    def test_parse_hosts_empty(self):
        """测试空主机文件解析"""
        hosts_content = "\n\n# 注释行\n"
        
        with patch("builtins.open", mock_open(read_data=hosts_content)):
            hosts, count = bgb.parse_hosts("dummy.txt")
            assert hosts == []
            assert count == 0


class TestLogging:
    """测试日志记录功能"""
    
    def test_log_with_ip_normal(self):
        """测试正常日志记录"""
        with patch("builtins.print") as mock_print:
            with patch("builtins.open", mock_open()) as mock_file:
                result = bgb.log_with_ip("***********", "测试消息", "test.log")
                
                # 检查打印输出
                mock_print.assert_called_once()
                
                # 检查文件写入
                mock_file.assert_called_with("test.log", "a")
                mock_file().write.assert_called_with("[***********-INFO] 测试消息\n")
    
    def test_log_with_ip_error(self):
        """测试错误日志记录"""
        with patch("builtins.print") as mock_print:
            with patch("builtins.open", mock_open()) as mock_file:
                result = bgb.log_with_ip("***********", "错误消息", "test.log", is_error=True)
                
                # 检查打印输出
                mock_print.assert_called_once()
                
                # 检查文件写入
                mock_file.assert_called_with("test.log", "a")
                mock_file().write.assert_called_with("[***********-ERROR] 错误消息\n")


class TestRemoteCheck:
    """测试远程检查功能"""
    
    @patch("paramiko.SSHClient")
    @patch("builtins.open", new_callable=mock_open)
    @patch("batch_gpu_burn.log_with_ip")
    def test_run_remote_check_success(self, mock_log, mock_file, mock_ssh):
        """测试成功的远程检查"""
        # 配置SSH模拟
        mock_client = MagicMock()
        mock_ssh.return_value = mock_client
        
        # 模拟channel
        mock_channel = MagicMock()
        mock_channel.recv_ready.side_effect = [True, False]
        mock_channel.recv_stderr_ready.return_value = False
        # 修复：使用ASCII字符或将中文字符编码为bytes
        mock_channel.recv.return_value = "测试输出\n".encode('utf-8')
        mock_channel.exit_status_ready.side_effect = [False, True]
        mock_channel.recv_exit_status.return_value = 0
        
        # 模拟exec_command
        mock_stdout = MagicMock()
        mock_stdout.channel = mock_channel
        mock_client.exec_command.return_value = (None, mock_stdout, None)
        
        # 执行测试
        result = bgb.run_remote_check("***********", 10, "/test.py", 5, 1)
        
        # 验证结果
        assert result[0] == "***********"  # 主机名
        assert result[1] == True  # 成功标志
        assert isinstance(result[2], float)  # 耗时
        
        # 验证SSH调用
        mock_client.connect.assert_called_once()
        mock_client.exec_command.assert_called_once()
        mock_client.close.assert_called_once()
    
    @patch("paramiko.SSHClient")
    @patch("builtins.open", new_callable=mock_open)
    @patch("batch_gpu_burn.log_with_ip")
    def test_run_remote_check_failure(self, mock_log, mock_file, mock_ssh):
        """测试失败的远程检查"""
        # 配置SSH模拟
        mock_client = MagicMock()
        mock_ssh.return_value = mock_client
        
        # 模拟连接异常
        mock_client.connect.side_effect = Exception("连接失败")
        
        # 执行测试
        result = bgb.run_remote_check("***********", 10, "/test.py", 5, 1)
        
        # 验证结果
        assert result[0] == "***********"  # 主机名
        assert result[1] == False  # 失败标志
        assert isinstance(result[2], float)  # 耗时
        
        # 验证SSH调用
        mock_client.connect.assert_called_once()
        mock_client.close.assert_called_once()


class TestMainFunction:
    """测试主函数功能"""
    
    @patch("batch_gpu_burn.parse_hosts")
    @patch("batch_gpu_burn.get_summary_log_name")
    @patch("batch_gpu_burn.run_remote_check")
    @patch("concurrent.futures.ThreadPoolExecutor")
    @patch("sys.exit")
    def test_main_success(self, mock_exit, mock_executor, mock_run, mock_log_name, mock_parse):
        """测试主函数成功执行"""
        # 模拟命令行参数
        test_args = ["batch_gpu_burn.py", "hosts.txt"]
        with patch("sys.argv", test_args):
            # 模拟主机解析
            mock_parse.return_value = (["host1", "host2"], 2)
            
            # 模拟日志文件名
            mock_log_name.return_value = Path("test.log")
            
            # 模拟远程检查结果 - 为每个主机创建一个future
            mock_futures = [MagicMock(), MagicMock()]
            mock_futures[0].result.return_value = ("host1", True, 10.0)
            mock_futures[1].result.return_value = ("host2", True, 10.0)
            
            mock_executor_instance = MagicMock()
            mock_executor_instance.__enter__.return_value.submit.side_effect = lambda *args, **kwargs: mock_futures.pop(0)
            mock_executor.return_value = mock_executor_instance
            
            # 执行测试
            with patch("builtins.open", mock_open()):
                with patch("batch_gpu_burn.Logger"):
                    bgb.main()
            
            # 验证调用
            mock_parse.assert_called_once()
            mock_log_name.assert_called_once()
            assert mock_executor_instance.__enter__.return_value.submit.call_count == 2  # 确保提交了两个任务
            mock_exit.assert_not_called()  # 成功时不应调用sys.exit
    
    @patch("batch_gpu_burn.parse_hosts")
    @patch("batch_gpu_burn.get_summary_log_name")
    @patch("batch_gpu_burn.run_remote_check")
    @patch("concurrent.futures.ThreadPoolExecutor")
    @patch("sys.exit")
    def test_main_with_failures(self, mock_exit, mock_executor, mock_run, mock_log_name, mock_parse):
        """测试主函数处理失败情况"""
        # 模拟命令行参数
        test_args = ["batch_gpu_burn.py", "hosts.txt"]
        with patch("sys.argv", test_args):
            # 模拟主机解析
            mock_parse.return_value = (["host1", "host2"], 2)
            
            # 模拟日志文件名
            mock_log_name.return_value = Path("test.log")
            
            # 模拟远程检查结果 - 一个成功，一个失败
            mock_futures = [MagicMock(), MagicMock()]
            mock_futures[0].result.return_value = ("host1", True, 10.0)
            mock_futures[1].result.return_value = ("host2", False, 10.0)
            
            mock_executor_instance = MagicMock()
            mock_executor_instance.__enter__.return_value.submit.side_effect = lambda *args, **kwargs: mock_futures.pop(0)
            mock_executor.return_value = mock_executor_instance
            
            # 执行测试
            with patch("builtins.open", mock_open()):
                with patch("batch_gpu_burn.Logger"):
                    bgb.main()
            
            # 验证调用
            mock_parse.assert_called_once()
            mock_log_name.assert_called_once()
            mock_exit.assert_called_once_with(1)  # 失败时应调用sys.exit(1)


if __name__ == "__main__":
    pytest.main(["-v", __file__])