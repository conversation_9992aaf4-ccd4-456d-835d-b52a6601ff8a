###
# @Author: wxwang
# @Script description: nccl 慢节点筛选，需要在容器内使用
###
import collections
import os
import random
import subprocess
import sys
import threading
import time
from queue import Queue
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
import concurrent.futures

# 全局配置
MAX_RETRIES = 1  # 最大重试次数
MAX_DURATION = 18000  # 300分钟超时(秒)
RETRY_DELAY = 5  # 重试间隔(秒)
MAX_THREADS = 100  # 最大并发线程数
MIN_GROUP_SIZE = 4  # 最小分组大小，小于此值不并发
MAX_RECURSION_DEPTH = 6  # 最大递归深度

# 带宽阈值（GB/s）
BANDWIDTH_THRESHOLD = {
    'critical': 70,  # 最低要求，低于此值视为异常节点
    'slow': 370,  # 慢节点阈值
    'good': 400  # 良好标准
}

# 全局日志文件变量
NCCL_LOG_FILE = ""
APP_LOG_FILE = ""


class Logger:
    """日志记录器，同时输出到控制台和文件"""

    def __init__(self, log_path):
        LOG_DIR.mkdir(exist_ok=True)
        self.console = sys.stdout
        self.log_file = open(log_path, 'a')

    def write(self, message):
        self.console.write(message)
        self.log_file.write(message)
        self.log_file.flush()

    def flush(self):
        self.console.flush()
        self.log_file.flush()

    def close(self):
        self.log_file.close()


def ensure_logs_dir():
    """确保logs目录存在"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs") if __file__ != "" else os.path.join(
        os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    return log_dir


def get_log_filenames(node_count):
    """根据节点数量生成日志文件名"""
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = ensure_logs_dir()
    return (
        os.path.join(log_dir, f"nccl_debug_{current_time}_{node_count}n.log"),
        os.path.join(log_dir, f"app_runtime_{current_time}_{node_count}n.log")
    )


def get_timestamp():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def log(message, level="INFO"):
    """带时间戳的日志记录"""
    global APP_LOG_FILE
    log_entry = f"[{get_timestamp()}] [{level}] {message}"
    print(log_entry)
    if APP_LOG_FILE:
        with open(APP_LOG_FILE, 'a') as f:
            f.write(log_entry + '\n')


def log_nccl_output(output, group_name=""):
    """带组标识的NCCL日志记录"""
    global NCCL_LOG_FILE
    if NCCL_LOG_FILE:
        with open(NCCL_LOG_FILE, 'a') as f:
            f.write(f"\n[{get_timestamp()}] [GROUP {group_name}] NCCL OUTPUT:\n")
            f.write(output + '\n')


def build_host_string(ip_list, slots_per_host=8):
    return ",".join([f"{ip}:{slots_per_host}" for ip in ip_list])


def check_ssh_connection(host, timeout=5):
    """检查SSH免密登录"""
    try:
        cmd = ["ssh", "-o", "BatchMode=yes", "-o", "ConnectTimeout=" + str(timeout),
               host, "echo SSH连接测试成功"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            log(f"SSH连接检查通过: {host}")
            return True
        else:
            log(f"SSH连接失败: {host} - 错误: {result.stderr.strip()}", "ERROR")
            return False
    except subprocess.TimeoutExpired:
        log(f"SSH连接超时: {host} (超过{timeout}秒)", "ERROR")
        return False
    except Exception as e:
        log(f"SSH连接异常: {host} - {str(e)}", "ERROR")
        return False


def check_all_nodes_ssh(nodes, max_threads=50):
    """并发SSH检查"""
    log("\n开始SSH免密登录检查...")
    failed_nodes = []

    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        results = list(executor.map(check_ssh_connection, nodes))

    failed_nodes = [node for node, success in zip(nodes, results) if not success]

    if failed_nodes:
        log(f"\nSSH检查失败节点 ({len(failed_nodes)}个): {failed_nodes}", "ERROR")
        log("请确保以下节点已配置SSH免密登录:", "ERROR")
        for node in failed_nodes:
            log(f"  ssh-copy-id {node}", "ERROR")
        return False
    return True


def allreduce_test(node_group, group_name="", check_slow=False):
    """执行NCCL测试并返回结果
    参数:
        node_group: 要测试的节点组
        group_name: 测试组名称
        check_slow: 是否进行慢节点检测（只在两两分组时为True）
    返回: (是否通过, 带宽值, 详细信息)
    """
    slots_per_host = 8
    host_string = build_host_string(node_group, slots_per_host)
    total_processes = len(node_group) * slots_per_host
    test_timeout = 80  # 测试超时时间(秒)

    test_id = f"{group_name}-{time.strftime('%m%d%H%M%S')}-{random.randint(1000, 9999)}"

    # 记录测试开始信息
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"\n\n{'=' * 80}\n")
        f.write(f"[TEST START] Group: {group_name} | ID: {test_id}\n")
        f.write(f"Nodes: {node_group}\n")
        f.write(f"Processes: {total_processes}\n")
        f.write(f"Start Time: {get_timestamp()}\n")
        f.write(f"{'=' * 80}\n")

    cmd = [
        "mpirun",
        "--allow-run-as-root",
        "-np", str(total_processes),
        "-host", host_string,
        "-x", "NCCL_DEBUG=INFO",
        "-x", "NCCL_IB_HCA=mlx5_100,mlx5_101,mlx5_102,mlx5_103,mlx5_104,mlx5_105,mlx5_106,mlx5_107",
        "-x", "NCCL_ALGO=Ring",
        "-x", "NCCL_IB_QPS_PER_CONNECTION=2",
        # "-x", "NCCL_PXN_DISABLE=0",
        "-x", "NCCL_MIN_NCHANNELS=24",
        "-x", "NCCL_SOCKET_IFNAME=bond0",
        "-x", "NCCL_IB_GID_INDEX=3",
        "-x", "NCCL_TIMEOUT=75",
        "./build/all_reduce_perf",
        "-b", "16G",
        "-e", "16G",
        "-g", "1",
        "-f", "2",
        "-T", "75"
    ]

    log(f"[测试 {group_name} | ID: {test_id}] 启动测试组")
    log(f"[测试 {group_name} | ID: {test_id}]   节点列表: {node_group}")
    log(f"[测试 {group_name} | ID: {test_id}]   总进程数: {total_processes}  (机器数: {len(node_group)})")
    log(f"[测试 {group_name} | ID: {test_id}]   完整命令: {' '.join(cmd)}")

    attempt = 0
    while attempt < MAX_RETRIES:
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=test_timeout)
            elapsed = time.time() - start_time

            # 记录详细结果
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"[TEST RESULT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Exit Code: {result.returncode}\n")
                f.write(f"Duration: {elapsed:.2f}s\n")
                f.write("=== STDOUT ===\n")
                f.write(result.stdout + "\n")
                if result.stderr:
                    f.write("=== STDERR ===\n")
                    f.write(result.stderr + "\n")

            # 解析带宽
            bandwidth = None
            for line in result.stdout.split('\n'):
                if "Avg bus bandwidth" in line:
                    try:
                        bandwidth = float(line.split()[-1])
                        break
                    except:
                        continue

            # 根据带宽判断状态
            if bandwidth is None:
                success = False
                status = "异常(无带宽数据)"
            elif bandwidth < BANDWIDTH_THRESHOLD['critical']:
                success = False
                status = "异常"
            elif check_slow and bandwidth < BANDWIDTH_THRESHOLD['slow']:
                success = True  # 测试通过但标记为慢节点
                status = "慢速"
            else:
                success = True
                status = "正常"

            log(f"[测试 {group_name} | ID: {test_id}] 测试结果: {status}, 带宽: {bandwidth or '无'} GB/s, 耗时: {elapsed:.2f}s")

            # 记录测试结束
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"[TEST END] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Status: {'SUCCESS' if success else 'FAILED'}\n")
                f.write(f"End Time: {get_timestamp()}\n")
                f.write(f"{'=' * 80}\n")

            # 等待显存释放
            wait_for_gpu_memory(node_group, f"{test_id}-测试后")
            time.sleep(15)

            return success, bandwidth, status

        except subprocess.TimeoutExpired as e:
            attempt += 1
            elapsed = time.time() - start_time
            # 等待显存释放
            wait_for_gpu_memory(node_group, f"{test_id}-超时重试{attempt}")
            time.sleep(15)
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次超时 (耗时{elapsed:.2f}s/{test_timeout}s)",
                "WARNING")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"[TEST TIMEOUT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Elapsed: {elapsed:.2f}s\n")
                f.write(f"Error: {str(e)}\n")

        except Exception as e:
            attempt += 1
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            # 等待显存释放
            wait_for_gpu_memory(node_group, f"{test_id}-异常重试{attempt}")
            time.sleep(15)
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次异常: {str(e)}", "ERROR")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"[TEST ERROR] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Error Type: {type(e).__name__}\n")
                f.write(f"Error Msg: {str(e)}\n")

    # 所有重试均失败
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"[TEST FAILED] Group: {group_name} | ID: {test_id}\n")
        f.write(f"All {MAX_RETRIES} attempts failed\n")
        f.write(f"{'=' * 80}\n")
    log(f"[测试 {group_name} | ID: {test_id}] 所有重试均失败", "ERROR")
    return False, None, "重试耗尽"


def shuffled_diff(previous_groups, current_nodes, witnesses=None):
    """确保新分组与历史分组不重复，并排除已确认正常的节点"""
    witnesses = witnesses or []
    # 过滤掉已确认正常的节点
    valid_nodes = [node for node in current_nodes if node not in witnesses]

    # 将历史分组转换为冻结集合的集合（顺序无关）
    previous_group_sets = {frozenset(group) for group in previous_groups}

    max_attempts = 10  # 防止无限循环
    for _ in range(max_attempts):
        random.shuffle(valid_nodes)
        # 生成当前分组方案（每组2节点）
        current_groups = [
            valid_nodes[i:i + 2]
            for i in range(0, len(valid_nodes), 2)
        ]

        # 检查是否与历史分组重复
        current_group_sets = {frozenset(group) for group in current_groups}
        if not current_group_sets & previous_group_sets:
            return valid_nodes

    return valid_nodes  # 如果所有尝试都失败，返回原始顺序


def prescreen(nodes, group_name="主组", witnesses=None, depth=0):
    """
    完整版NCCL慢节点筛查逻辑
    返回: (异常节点列表, 慢节点列表, 正常节点列表)
    """
    # 初始化
    witnesses = witnesses or []
    nodes = [n for n in nodes if n not in witnesses]
    suspects = nodes.copy()
    slow_nodes = []

    # 保留组名称处理
    current_group = f"{group_name}-D{depth}" if depth > 0 else group_name

    log(f"{'=' * 50}")
    log(f"[筛查 {current_group}] 启动筛查 | 节点数: {len(nodes)}")
    log(f"{'=' * 50}")

    # === 第1步：完整组测试 ===
    log(f"[阶段1] 完整组测试 ({len(suspects)}节点)")
    success, bandwidth, status = allreduce_test(suspects, f"{current_group}-完整组", check_slow=False)
    if success:
        witnesses.extend(suspects)
        return [], [], witnesses

    # 等待显存释放
    wait_for_gpu_memory(f"{current_group}-完整组后")
    time.sleep(15)

    # === 第2步：两两分组测试（含重试）===
    passed_groups, slow_groups, failed_nodes = run_paired_testing(suspects, current_group)

    # === 第3步：合并验证 ===
    passed_nodes = [node for group in passed_groups for node in group]
    slow_nodes = [node for group, _ in slow_groups for node in group]

    if not passed_groups and not slow_groups:
        log("所有两两分组均失败，无法缩小问题范围", "ERROR")
        return suspects, [], witnesses

    # 验证正常节点前等待显存释放
    wait_for_gpu_memory(f"{current_group}-合并验证前")
    time.sleep(15)

    # 验证正常节点
    if passed_nodes:
        success, bandwidth, status = allreduce_test(passed_nodes, f"{current_group}-正常组合并验证", check_slow=False)
        if success:
            witnesses.extend(passed_nodes)
        else:
            # 如果合并验证失败，进入二分验证
            suspects = binary_verify(passed_nodes, failed_nodes, current_group, witnesses, depth)

    # 返回前确保去重
    witnesses = list(set(witnesses))
    slow_nodes = list(set(slow_nodes))
    suspects = list(set(failed_nodes) - set(witnesses))

    log(f"{'=' * 50}")
    log(f"[筛查 {current_group}] 筛查完成")
    log(f"正常节点: {len(witnesses)}个")
    if slow_nodes:
        log(f"待验证慢速节点 ({len(slow_nodes)}个): {sorted(slow_nodes)}", "WARNING")
    else:
        log(f"待验证慢速节点: {len(slow_nodes)}个")
    log(f"异常节点 ({len(suspects)}个): {sorted(suspects)}", "ERROR" if suspects else "INFO")
    log(f"{'=' * 50}")

    return suspects, slow_nodes, witnesses


def run_paired_testing(nodes, group_name, max_retries=2):
    """执行两两分组测试（确保每次重试分组不同）"""
    passed_groups = []  # 正常节点组
    slow_groups = []  # 慢节点组
    failed_nodes = []  # 异常节点
    previous_groupings = []  # 记录历史分组方案

    for retry in range(max_retries + 1):
        # 生成全新分组方案（确保与之前不同）
        new_nodes = generate_unique_grouping(nodes, previous_groupings)
        subgroups = [new_nodes[i:i + 2] for i in range(0, len(new_nodes), 2)]
        previous_groupings.append(subgroups)  # 记录本次分组

        # 并发测试
        with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            futures = {
                executor.submit(allreduce_test, group, f"{group_name}-小组-{i}-重试{retry}", True): group
                for i, group in enumerate(subgroups)
            }
            for future in futures:
                group = futures[future]
                try:
                    success, bandwidth, status = future.result()
                    if success:
                        if status == "慢速":
                            slow_groups.append((group, bandwidth))
                        else:
                            passed_groups.append(group)
                    else:
                        failed_nodes.extend(group)
                except Exception as e:
                    log(f"小组测试异常: {str(e)}", "ERROR")
                    failed_nodes.extend(group)

        if (passed_groups or slow_groups) or retry == max_retries:
            break

        log(f"第{retry + 1}次重试将使用全新分组方案", "INFO")

    # 记录慢节点信息
    if slow_groups:
        log("=== 发现潜在的慢节点组 ===")
        for group, bandwidth in slow_groups:
            log(f"节点组: {sorted(group)} - 带宽: {bandwidth:.2f} GB/s (低于{BANDWIDTH_THRESHOLD['slow']}GB/s)", "WARNING")
        log("=" * 50)

    return passed_groups, slow_groups, failed_nodes


def generate_unique_grouping(nodes, previous_groupings):
    """生成与历史分组不同的新方案"""
    max_attempts = 10
    for _ in range(max_attempts):
        # 方法1：随机打乱+验证
        shuffled = nodes.copy()
        random.shuffle(shuffled)
        new_groups = [shuffled[i:i + 2] for i in range(0, len(shuffled), 2)]

        # 检查是否与历史分组重复
        is_unique = True
        for past in previous_groupings:
            if collections.Counter(frozenset(pair) for pair in past) == \
                    collections.Counter(frozenset(pair) for pair in new_groups):
                is_unique = False
                break

        if is_unique:
            return shuffled

    # 方法2：强制不同（当随机无法满足时）
    log("随机打乱无法获得新分组，使用轮换法", "WARNING")
    return nodes[1:] + [nodes[0]]


def handle_three_nodes(nodes, group_name, witnesses=None, depth=0):
    """
    完整版3节点处理逻辑
    功能：
    1. 先进行完整组测试
    2. 完整组失败后穷举所有2节点组合测试
    3. 严格验证通过组
    4. 完善的日志记录
    参数：
        nodes: 待测的3个节点IP列表，如 ["*********", "*********", "*********"]
        group_name: 测试组标识
        witnesses: 已知正常节点列表
        depth: 递归深度
    返回：
        (可疑节点列表, 确认正常节点列表)
    """
    if len(nodes) != 3:
        raise ValueError("handle_three_nodes只接受3个节点")

    current_group = f"{group_name}-3节点-D{depth}" if depth > 0 else f"{group_name}-3节点"
    node_a, node_b, node_c = nodes  # 解构节点IP

    log(f"\n{'=' * 60}")
    log(f"[筛查 {current_group}] 启动3节点深度检测")
    log(f"待测节点: {nodes}")
    log(f"{'=' * 60}")

    # 首先进行完整组测试
    log(f"\n[测试] 完整3节点组测试")
    if allreduce_test(nodes, f"{current_group}-完整组"):
        log("完整组测试通过，所有节点正常")
        return [], witnesses + nodes if witnesses else nodes

    # 完整组测试失败，继续两两组合测试
    log("完整组测试失败，开始两两组合测试", "WARNING")

    # 测试所有2节点组合（AB, AC, BC）
    test_cases = [
        ([node_a, node_b], f"{current_group}-{node_a}_{node_b}"),
        ([node_a, node_c], f"{current_group}-{node_a}_{node_c}"),
        ([node_b, node_c], f"{current_group}-{node_b}_{node_c}")
    ]

    # 执行所有测试（带超时处理）
    results = {}
    for nodes_pair, test_name in test_cases:
        log(f"\n[测试] 组合 {nodes_pair}")
        try:
            results[tuple(nodes_pair)] = allreduce_test(nodes_pair, test_name)
        except Exception as e:
            log(f"组合测试异常: {str(e)}", "ERROR")
            results[tuple(nodes_pair)] = False

    # 结果分析
    passed_pairs = [pair for pair, passed in results.items() if passed]
    failed_pairs = [pair for pair, passed in results.items() if not passed]

    # 情况1：全部组合失败 → 可能多个坏节点
    if not passed_pairs:
        log("所有2节点组合均失败，可能多个坏节点或网络问题", "WARNING")
        return nodes, []  # 全部标记为可疑

    # 情况2：只有1个组合通过 → 需要验证
    elif len(passed_pairs) == 1:
        good_nodes = list(passed_pairs[0])
        bad_node = list(set(nodes) - set(good_nodes))[0]

        # 关键验证：确保通过组真实有效
        log(f"\n[验证] 确认正常组: {good_nodes}")
        if allreduce_test(good_nodes, f"{current_group}-最终验证"):
            log(f"确认异常节点: {bad_node} | 正常节点: {good_nodes}")
            return [bad_node], good_nodes
        else:
            log("正常组验证失败，所有节点可疑", "WARNING")
            return nodes, []

    # 情况3：多个组合通过 → 矛盾结果
    else:
        log("矛盾结果：多个组合通过但完整组失败", "ERROR")
        return nodes, []  # 保守返回全部可疑


def binary_verify(nodes, existing_failed, group_name, witnesses, depth):
    """并发二分验证（核心改进点）"""
    if depth >= MAX_RECURSION_DEPTH:
        log(f"达到最大递归深度{depth}，终止二分", "WARNING")
        return nodes + existing_failed

    while len(nodes) > 2:
        # 等待显存释放
        wait_for_gpu_memory(nodes, f"{group_name}-二分前")
        time.sleep(15)

        mid = len(nodes) // 2
        left, right = nodes[:mid], nodes[mid:]

        log(f"[二分验证 {group_name}] 并发测试子组 | "
            f"左组: {left} | 右组: {right}")

        # 并发测试（带超时控制）
        left_status = right_status = None
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = {
                "left": executor.submit(allreduce_test, left, f"{group_name}-二分左-D{depth}", False),
                "right": executor.submit(allreduce_test, right, f"{group_name}-二分右-D{depth}", False)
            }

            # 非阻塞获取结果
            for side in futures:
                try:
                    success, bandwidth, status = futures[side].result(timeout=95)  # 额外15秒缓冲
                    if success:  # 只关注测试是否通过，不考虑慢节点
                        witnesses.extend(eval(side))
                        log(f"{side}子组验证通过: {eval(side)}", "INFO")
                        locals()[f"{side}_status"] = "passed"
                    else:
                        locals()[f"{side}_status"] = "failed"
                except TimeoutError:
                    log(f"{side}子组测试超时: {eval(side)}", "WARNING")
                    locals()[f"{side}_status"] = "timeout"

        # 决策逻辑（只关注成功/失败）
        if left_status == "passed" and right_status == "passed":
            return existing_failed  # 两侧都通过，不需要继续递归
        elif left_status == "passed":
            nodes = right  # 右组有问题
        elif right_status == "passed":
            nodes = left  # 左组有问题
        else:
            # 两侧都有问题，递归处理
            log("两侧子组均异常，递归处理", "INFO")
            left_suspects = binary_verify(left, [], group_name, witnesses, depth + 1)
            right_suspects = binary_verify(right, [], group_name, witnesses, depth + 1)
            return left_suspects + right_suspects + existing_failed

    return nodes + existing_failed


def cross_validate_two_nodes(nodes, existing_failed, group_name, witnesses):
    """对最后2节点进行交叉验证"""
    node_a, node_b = nodes

    # 验证节点A（与已知正常节点组队）
    test_group_a = random.sample(witnesses, min(2, len(witnesses))) + [node_a]
    a_ok = allreduce_test(test_group_a, f"{group_name}-交叉验证-A", True)

    # 验证节点B（与已知正常节点组队）
    test_group_b = random.sample(witnesses, min(2, len(witnesses))) + [node_b]
    b_ok = allreduce_test(test_group_b, f"{group_name}-交叉验证-B", True)

    # 结果判定
    if a_ok and not b_ok:
        return [node_b], witnesses + [node_a]
    elif b_ok and not a_ok:
        return [node_a], witnesses + [node_b]
    else:
        return nodes + existing_failed, witnesses  # 都失败或都通过


def validate_suspects(suspects, slow_nodes, witnesses):
    """并发验证可疑节点和慢节点，确保不会重复使用相同节点"""
    # 首先确保输入列表不重叠
    suspects = [node for node in suspects if node not in witnesses]
    slow_nodes = [node for node in slow_nodes if node not in witnesses]
    witnesses = [node for node in witnesses if node not in suspects and node not in slow_nodes]

    if not suspects and not slow_nodes:
        log("[验证阶段] 没有节点需要验证", "INFO")
        return [], [], witnesses

    if len(witnesses) < 2:
        log("[验证阶段] 正常节点不足，无法验证", "WARNING")
        return suspects, slow_nodes, witnesses

    log(f"\n[验证阶段] ============ 开始并发验证 ============")
    if suspects:
        log(f"待验证异常节点: {len(suspects)}个")
    if slow_nodes:
        log(f"待验证慢速节点: {len(slow_nodes)}个")

    # 使用队列管理待验证节点和可用witnesses
    verify_queue = Queue()
    for node in suspects:
        verify_queue.put(("suspect", node))
    for node in slow_nodes:
        verify_queue.put(("slow", node))

    # 使用线程安全的witnesses池
    witnesses_pool = witnesses.copy()
    witnesses_lock = threading.Lock()

    # 结果存储
    results = []
    results_lock = threading.Lock()

    def worker():
        while not verify_queue.empty():
            try:
                node_type, node = verify_queue.get_nowait()

                # 获取2个witnesses（线程安全）
                with witnesses_lock:
                    if len(witnesses_pool) < 2:
                        log(f"[验证阶段] 正常节点不足，跳过验证 {node}", "WARNING")
                        with results_lock:
                            results.append((node_type, node, False, None))
                        continue

                    # 随机选择2个witnesses
                    selected = random.sample(witnesses_pool, 2)
                    for w in selected:
                        witnesses_pool.remove(w)

                # 执行验证测试
                test_group = selected + [node]
                group_name = f"验证-{node}"
                success, bandwidth, status = allreduce_test(test_group, group_name, True)

                # 归还witnesses
                with witnesses_lock:
                    witnesses_pool.extend(selected)

                # 记录结果
                with results_lock:
                    results.append((node_type, node, success, status))
                    if node_type == "suspect":
                        result_str = "通过" if success else "失败"
                    else:  # slow node
                        if not success:
                            result_str = "异常"
                        elif status == "慢速":
                            result_str = f"确认慢速(带宽: {bandwidth:.2f}GB/s)"
                        else:
                            result_str = "恢复正常"
                    log(f"[验证阶段] 节点 {node} ({node_type}) 验证结果: {result_str}")

            except Exception as e:
                log(f"[验证阶段] 验证节点异常: {str(e)}", "ERROR")

    # 启动线程池
    threads = []
    max_workers = min(MAX_THREADS, len(suspects) + len(slow_nodes), len(witnesses) // 2)
    for _ in range(max_workers):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)

    # 等待所有线程完成
    for t in threads:
        t.join()

    # 处理结果
    confirmed_suspects = []
    confirmed_slow = []
    new_witnesses = []

    for node_type, node, success, status in results:
        if node_type == "suspect":
            if success:
                new_witnesses.append(node)
            else:
                confirmed_suspects.append(node)
        else:  # slow node
            if not success:
                confirmed_suspects.append(node)
            elif status == "慢速":
                confirmed_slow.append(node)
            else:
                new_witnesses.append(node)

    # 更新witnesses列表
    witnesses.extend(new_witnesses)

    # 结果汇总
    log("\n[验证阶段] ============ 验证结果 ============")
    if new_witnesses:
        log(f"恢复正常节点 ({len(new_witnesses)}个): {sorted(new_witnesses)}")
    if confirmed_slow:
        log(f"确认慢速节点 ({len(confirmed_slow)}个): {sorted(confirmed_slow)}", "WARNING")
    if confirmed_suspects:
        log(f"确认异常节点 ({len(confirmed_suspects)}个): {sorted(confirmed_suspects)}", "ERROR")

    return confirmed_suspects, confirmed_slow, witnesses


def force_free_gpu_memory_remote(node):
    """
    在远程节点上强制释放GPU显存
    参数:
        node: 目标节点IP
    返回:
        bool: 是否成功释放
    """
    try:
        # 1. 在远程机器上查找并终止NCCL测试进程
        kill_cmd = "pgrep -f all_reduce_perf | xargs -r kill -9"
        ssh_cmd = f"ssh -o BatchMode=yes -o ConnectTimeout=10 {node} '{kill_cmd}'"
        subprocess.run(ssh_cmd, shell=True, check=False)

        # 2. 在远程机器上重置GPU
        reset_cmd = "nvidia-smi --gpu-reset"
        ssh_cmd = f"ssh -o BatchMode=yes -o ConnectTimeout=10 {node} '{reset_cmd}'"
        subprocess.run(ssh_cmd, shell=True, check=False)

        log(f"节点 {node} GPU资源已强制释放", "WARNING")
        return True
    except Exception as e:
        log(f"节点 {node} 强制释放GPU资源失败: {str(e)}", "ERROR")
        return False


def check_gpu_memory_remote(node, max_wait=60, check_interval=1, force_free=True):
    """
    检查远程节点的GPU显存是否已释放
    参数:
        node: 目标节点IP
        max_wait: 最大等待时间（秒）
        check_interval: 检查间隔（秒）
        force_free: 是否在超时后尝试强制释放
    返回:
        bool: 显存是否已释放到安全水平
    """
    start_time = time.time()
    memory_threshold = 1000  # 1000MB为安全阈值
    force_free_attempted = False

    while time.time() - start_time < max_wait:
        try:
            # 在远程机器上执行nvidia-smi命令
            cmd = "nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits"
            ssh_cmd = f"ssh -o BatchMode=yes -o ConnectTimeout=10 {node} '{cmd}'"
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                log(f"无法获取节点 {node} 的GPU显存信息: {result.stderr}", "WARNING")
                return True

            # 检查每个GPU的显存使用
            memory_usage = [int(mem.strip()) for mem in result.stdout.strip().split('\n')]
            max_usage = max(memory_usage)

            if max_usage < memory_threshold:
                if force_free_attempted:
                    log(f"节点 {node} 强制释放后GPU显存已释放 (最大使用: {max_usage}MB)")
                else:
                    log(f"节点 {node} GPU显存已释放 (最大使用: {max_usage}MB)")
                return True

            # 如果显存使用较高且已经等待了一半时间，尝试强制释放
            if force_free and not force_free_attempted and (time.time() - start_time) > max_wait / 2:
                log(f"节点 {node} 显存使用仍然较高 ({max_usage}MB)，尝试强制释放", "WARNING")
                force_free_gpu_memory_remote(node)
                force_free_attempted = True
                continue

            log(f"等待节点 {node} GPU显存释放 (当前最大使用: {max_usage}MB)", "INFO")
            time.sleep(check_interval)

        except subprocess.TimeoutExpired:
            log(f"节点 {node} GPU显存检查超时", "WARNING")
            return True
        except Exception as e:
            log(f"节点 {node} GPU显存检查异常: {str(e)}", "WARNING")
            return True

    # 如果等待超时，最后尝试强制释放
    if force_free and not force_free_attempted:
        log(f"节点 {node} 等待超时，尝试强制释放显存", "WARNING")
        if force_free_gpu_memory_remote(node):
            # 再次检查显存状态
            try:
                cmd = "nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits"
                ssh_cmd = f"ssh -o BatchMode=yes -o ConnectTimeout=5 root@{node} '{cmd}'"
                result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    memory_usage = [int(mem.strip()) for mem in result.stdout.strip().split('\n')]
                    max_usage = max(memory_usage)
                    if max_usage < memory_threshold:
                        log(f"节点 {node} 强制释放成功，显存已释放 (最大使用: {max_usage}MB)")
                        return True
            except:
                pass

    log(f"节点 {node} GPU显存等待超时 (已等待{max_wait}秒)", "WARNING")
    return False


def wait_for_gpu_memory(nodes, test_name="", timeout=60):
    """
    等待一组节点的GPU显存释放
    参数:
        nodes: 节点列表或单个节点
        test_name: 测试名称，用于日志
        timeout: 超时时间（秒）
    """
    # 确保nodes是列表类型
    if isinstance(nodes, str):
        nodes = [nodes]
    elif not isinstance(nodes, (list, tuple)):
        log(f"无效的节点参数类型: {type(nodes)}", "ERROR")
        return False

    # 过滤掉非IP地址的节点
    valid_nodes = []
    for node in nodes:
        # 简单的IP地址格式检查
        if isinstance(node, str) and all(part.isdigit() and 0 <= int(part) <= 255
                                         for part in node.split('.')):
            valid_nodes.append(node)

    if not valid_nodes:
        log("没有有效的节点需要检查", "WARNING")
        return True

    if test_name:
        log(f"[{test_name}] 等待GPU显存释放...")

    # 创建线程池
    executor = ThreadPoolExecutor(max_workers=min(MAX_THREADS, len(valid_nodes)))
    futures = []

    # 启动所有检查任务
    for node in valid_nodes:
        future = executor.submit(check_gpu_memory_remote, node, timeout)
        futures.append((node, future))

    # 等待所有任务完成或超时
    not_released = []
    start_time = time.time()
    remaining_time = timeout

    while futures and remaining_time > 0:
        # 使用as_completed等待任务完成
        done, not_done = concurrent.futures.wait(
            [f for _, f in futures],
            timeout=remaining_time,
            return_when=concurrent.futures.FIRST_COMPLETED
        )

        # 处理已完成的任务
        for node, future in futures[:]:
            if future in done:
                try:
                    if not future.result():
                        not_released.append(node)
                except Exception as e:
                    log(f"节点 {node} 显存检查失败: {str(e)}", "ERROR")
                    not_released.append(node)
                futures.remove((node, future))

        # 更新剩余时间
        remaining_time = timeout - (time.time() - start_time)

    # 处理超时的任务
    for node, future in futures:
        if not future.done():
            future.cancel()
            log(f"节点 {node} 显存检查超时", "WARNING")
            not_released.append(node)

    # 关闭线程池
    executor.shutdown(wait=False)

    if not_released:
        log(f"[{test_name}] 以下节点GPU显存未能完全释放: {not_released}", "WARNING")
        # 对未释放的节点尝试强制释放
        for node in not_released:
            force_free_gpu_memory_remote(node)

    return len(not_released) == 0


def main():
    global NCCL_LOG_FILE, APP_LOG_FILE

    if len(sys.argv) != 2:
        print("使用方法: python allreduce_checker.py <节点列表文件路径>")
        sys.exit(1)

    # 读取节点列表
    try:
        with open(sys.argv[1], 'r') as file:
            nodes = [line.strip() for line in file if line.strip()]
    except Exception as e:
        print(f"无法读取节点文件: {str(e)}")
        sys.exit(1)

    if not nodes:
        print("错误: 节点列表为空")
        sys.exit(1)

    # 设置日志文件
    NCCL_LOG_FILE, APP_LOG_FILE = get_log_filenames(len(nodes))

    with open(NCCL_LOG_FILE, 'w') as f:
        f.write(f"NCCL调试日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")
    with open(APP_LOG_FILE, 'w') as f:
        f.write(f"应用运行日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")

    log(f"从文件成功加载 {len(nodes)} 个节点")
    start_time = time.time()

    # 检查SSH连通性
    if not check_all_nodes_ssh(nodes):
        log("SSH免密登录检查未通过，程序终止", "ERROR")
        sys.exit(1)

    # 主筛查流程
    suspects, slow_nodes, witnesses = prescreen(nodes)
    # 等待显存释放
    time.sleep(15)
    suspects, slow_nodes, witnesses = validate_suspects(suspects, slow_nodes, witnesses)

    # 结果汇总
    log(f"\n============ 测试总结 ============")
    log(f"总耗时: {time.time() - start_time:.2f}秒")
    log("\n============ 节点状态汇总 ============")
    log(f"正常节点 ({len(witnesses)}个): {sorted(witnesses)}")
    if slow_nodes:
        log(f"慢速节点 ({len(slow_nodes)}个): {sorted(slow_nodes)}", "WARNING")
        log(f"  带宽低于 {BANDWIDTH_THRESHOLD['slow']} GB/s", "WARNING")
    log(f"异常节点 ({len(suspects)}个): {sorted(suspects)}", "ERROR" if suspects else "INFO")

    log("\n============ 日志文件 ============")
    log(f"NCCL调试日志已保存至: {NCCL_LOG_FILE}")
    log(f"应用运行日志已保存至: {APP_LOG_FILE}")

    # 如果有异常节点或慢节点，返回非零退出码
    if suspects or slow_nodes:
        if suspects:
            log(f"\n警告: {len(suspects)}台主机测试失败，请检查详细日志", "ERROR")
        if slow_nodes:
            log(f"\n警告: {len(slow_nodes)}台主机性能不达标，带宽低于{BANDWIDTH_THRESHOLD['slow']}GB/s", "WARNING")
        sys.exit(1)


if __name__ == "__main__":
    main()