version: '3.8'

services:
  nccl-test-runner:
    build: .
    privileged: true  # 需要特权模式访问GPU和网络设备
    network_mode: host  # 使用主机网络
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # 访问Docker daemon
      - .:/app  # 挂载当前目录
    environment:
      - NVIDIA_VISIBLE_DEVICES=all  # 暴露所有GPU
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    command: >
      python nccl-test-docker.py
      --gpu_num 8
      --ib_num 1
      --nodes hs[01-04]
      --image dockerhub.aicp.local/aicp-common/aicp_common/nccl_test:v3.0
      --mode random
      --NCCL_ALGO Tree,Ring 