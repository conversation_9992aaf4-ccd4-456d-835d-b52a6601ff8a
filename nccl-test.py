import datetime
import json
import os.path
import sys
import time
import traceback
from copy import deepcopy
import random

import yaml

base_path = os.path.dirname(os.path.abspath(__file__))
test_report_name = "test-report-%s.md" % datetime.datetime.now().strftime("%Y%m%d%H%M%S")
test_report_path = os.path.join(base_path, test_report_name)
k8s_yaml_dir = os.path.join(base_path, f"k8s-yaml-{int(time.time())}")
os.mkdir(k8s_yaml_dir)

import argparse
import re

index = 0

mpi_jobs = []
need_clean_up_jobs = []


def run_cmd(cmd):
    # run command and return output
    return os.popen(cmd).read()


class NcclTest:

    def __init__(self, nodes):
        global index
        self.nodes = nodes
        self.index = index
        self.job_name = f"nccl-test-{index:02}"
        self.yaml_path = os.path.join(k8s_yaml_dir, f"{self.job_name}.yaml")
        self.pod_name = self.job_name + "-launcher"
        self._status = ""
        self._completed = False
        self._logs = ""
        self._bandwidth = ""
        self.mpi_job = None
        index += 1

    def create_nccl_test_yaml(self):
        base_command = [
            "mpirun",
            "--allow-run-as-root",
            "--report-bindings",
            "-x",
            "NCCL_IB_TC=160",
            "-x",
            f"NCCL_IB_QPS_PER_CONNECTION={args.NCCL_IB_QPS_PER_CONNECTION.strip()}",
            "-x",
            "NCCL_IB_HCA=mlx5",
            "-x",
            f"NCCL_ALGO={args.NCCL_ALGO.strip()}",
            "-x",
            f"NCCL_NET_GDR_LEVEL={args.NCCL_NET_GDR_LEVEL.strip()}",
            "-x",
            "NCCL_DEBUG=TRACE",
            "-x",
            "NCCL_IB_DISABLE=0",
            "-x",
            "NCCL_IB_MERGE_VFS=0",
            "-x",
            f"NCCL_NVLS_ENABLE={args.NCCL_NVLS_ENABLE.strip()}",
            "-N",
            "1",
            "-n",
            str(len(self.nodes)),
            "--bind-to",
            "none",
            "-mca",
            "btl_base_verbose",
            "100",
            "-mca",
            "orte_base_help_aggregate",
            "0",
            "-mca",
            "btl_openib_allow_ib",
            "1",
            "-mca",
            "btl_openib_warn_default_gid_prefix",
            "0",
        ]

        if args.enable_sharp:
            base_command += [
                "-mca",
                "coll_hcoll_enable",
                "1",
                "-mca",
                "pml",
                "ucx",
                "-mca",
                "pml_ucx_verbose",
                "0",
                "-x",
                "UCX_TLS=ib",
                "-x",
                "SHARP_COLL_ENABLE_SAT=1",
                "-x",
                "SHARP_COLL_LOG_LEVEL=3",
                "-x",
                "SHARP_COLL_SAT_THRESHOLD=1",
                "-x",
                "CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7",
                "-x",
                "NCCL_P2P_DISABLE=0",
                "-x",
                "NCCL_SHM_DISABLE=0",
                "-x",
                "NCCL_NET_GDR_READ=1",
                "-x",
                "NCCL_SHARP_MAX_COMMS=2",
                "-x",
                "NCCL_COLLNET_ENABLE=1",
                "-x",
                "NCCL_NVLSTREE_MAX_CHUNKSIZE=131072",
                "-x",
                "NCCL_TOPO_DUMP_FILE=topodump-8nodes_8nic_sharp.xml",
            ]
        base_command += [
            "/opt/nccl-tests/build/all_reduce_perf",
            "-b",
            "1G",
            "-e",
            "1G",
            "-f",
            "2",
            "-g",
            str(args.gpu_num),
        ]

        self.mpi_job = {
            "apiVersion": "kubeflow.org/v1",
            "kind": "MPIJob",
            "metadata": {
                "name": self.job_name,
                "namespace": "nccl-test",
            },
            "spec": {
                "slotsPerWorker": 1,
                "runPolicy": {
                    "cleanPodPolicy": "Running"
                },
                "mpiReplicaSpecs": {
                    "Launcher": {
                        "replicas": 1,
                        "template": {
                            "spec": {
                                "affinity": {
                                    "nodeAffinity": {
                                        "requiredDuringSchedulingIgnoredDuringExecution": {
                                            "nodeSelectorTerms": [
                                                {
                                                    "matchExpressions": [
                                                        {
                                                            "key": "kubernetes.io/hostname",
                                                            "operator": "In",
                                                            "values": self.nodes
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                },
                                "containers": [
                                    {
                                        "securityContext": {
                                            "capabilities": {
                                                "add": [
                                                    "IPC_LOCK",
                                                    "SYS_RESOURCE"
                                                ]
                                            },
                                        },
                                        "image": args.image,
                                        "imagePullPolicy": "Always",
                                        "name": "nccl-test",
                                        "command": base_command
                                    }
                                ],
                                "tolerations": [
                                    {
                                        "operator": "Exists"
                                    }
                                ]
                            }
                        }
                    },
                    "Worker": {
                        "replicas": len(self.nodes),
                        "template": {
                            "spec": {
                                "affinity": {
                                    "nodeAffinity": {
                                        "requiredDuringSchedulingIgnoredDuringExecution": {
                                            "nodeSelectorTerms": [
                                                {
                                                    "matchExpressions": [
                                                        {
                                                            "key": "kubernetes.io/hostname",
                                                            "operator": "In",
                                                            "values": self.nodes
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                },
                                "volumes": [{
                                    "emptyDir": {
                                        "medium": "Memory",
                                        "sizeLimit": "4Gi"
                                    },
                                    "name": "volume-emptydir"
                                }],
                                "containers": [
                                    {
                                        "securityContext": {
                                            "capabilities": {
                                                "add": [
                                                    "IPC_LOCK",
                                                    "SYS_RESOURCE"
                                                ]
                                            },
                                        },
                                        "image": args.image,
                                        "imagePullPolicy": "Always",
                                        "name": "nccl-test",
                                        "volumeMounts": [{
                                            "name": "volume-emptydir",
                                            "mountPath": "/dev/shm"
                                        }],
                                        "resources": {
                                            "limits": {
                                                "nvidia.com/gpu": args.gpu_num,
                                                "nvidia.com/hostdev": args.ib_num
                                            }
                                        }
                                    }
                                ],
                                "tolerations": [
                                    {
                                        "operator": "Exists"
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        }
        # write mpijob to self.yaml_path as yaml format
        with open(self.yaml_path, "w", encoding="utf-8") as f:
            f.write(yaml.dump(self.mpi_job))

    def create_mpi_nccl_job(self):
        self.create_nccl_test_yaml()
        run_cmd(f"kubectl apply -f {self.yaml_path}")
        return self.mpi_job

    def get_mpi_nccl_job(self):
        # return custom_api.get_namespaced_custom_object("kubeflow.org", "v1", "nccl-test", "mpijobs", self.job_name)
        return json.loads(run_cmd(f"kubectl get mpijob {self.job_name} -n nccl-test -o json"))

    def delete_mpi_nccl_job(self):
        # custom_api.delete_namespaced_custom_object("kubeflow.org", "v1", "nccl-test", "mpijobs", self.job_name)
        run_cmd(f"kubectl delete mpijob {self.job_name} -n nccl-test")

    @property
    def status(self):
        if self._status:
            return self._status
        mpi_job = self.get_mpi_nccl_job()
        return mpi_job.get("status", {}).get("conditions", [{}])[-1].get("type", "Submitted")

    @property
    def bandwidth(self):
        if self._bandwidth:
            return self._bandwidth
        if self.completed:
            self._bandwidth = get_bandwidth(self.logs)
            return self._bandwidth
        return "N/A"

    @property
    def logs(self):
        if self._logs:
            return self._logs
        if self.completed:
            # self._logs = core_v1_api.read_namespaced_pod_log(self.pod_name, "nccl-test")
            self._logs = run_cmd(f"kubectl logs {self.pod_name} -n nccl-test -c nccl-test")
            return self._logs
        return ""

    @property
    def completed(self):
        if self._completed:
            return self._completed
        mpi_job = self.get_mpi_nccl_job()
        launcher = mpi_job.get("status", {}).get("replicaStatuses", {}).get("Launcher", {})
        if launcher.get("succeeded") == 1:
            self._completed = True
            self._status = "Succeeded"
        elif launcher.get("failed") == 1:
            self._completed = True
            self._status = "Failed"
        else:
            self._completed = False
        return self._completed

    def __repr__(self):
        return f"Job {self.job_name} completed: {str(self.completed):<5}, status: {self.status:<10}, bandwidth: {self.bandwidth:<8}, nodes: {self.nodes}"

    def __str__(self):
        return self.__repr__()


def create_nccl_test_namespace():
    if "NotFound" in run_cmd("kubectl get namespace nccl-test"):
        print("Namespace nccl-test not found, creating...")
        run_cmd("kubectl create namespace nccl-test")
    else:
        print("Namespace nccl-test already exists.")


def create_hostdev():
    print("Creating HostDeviceNetwork 'nccl-test-hostdevice-net'...")
    run_cmd("kubectl apply -f %s" % os.path.join(base_path, "host_dev.yaml"))
    print("HostDeviceNetwork 'nccl-test-hostdevice-net' created.")


# 定义一个函数来扩展节点范围
def expand_nodes(nodes):
    expanded_nodes = []
    pattern = re.compile(r'\[(\d+)-(\d+)]')  # 匹配 [01-10] 这种模式的正则表达式
    for node in nodes:
        match = pattern.search(node)
        if match:
            start = int(match.group(1))
            end = int(match.group(2))
            expanded_nodes.extend([f"{node[:match.start()]}{i:02d}" for i in range(start, end + 1)])
        else:
            expanded_nodes.append(node)
    return expanded_nodes


def run_group_nccl_test(group_nodes):
    global index
    tmp_mpi_jobs = []

    for group in group_nodes:
        nccl_test = NcclTest(group)
        nccl_test.create_mpi_nccl_job()
        tmp_mpi_jobs.append(nccl_test)
        need_clean_up_jobs.append(nccl_test)
    for i in range(60):
        print(f"Checking job status...{'#' * (i + 1)}")
        for mpijob in tmp_mpi_jobs:
            print(mpijob)
        if all([mpijob.completed for mpijob in tmp_mpi_jobs]):
            print("\nAll one node jobs completed.")
            break
        time.sleep(10)
        sys.stdout.write("\033[F\033[K" * (len(tmp_mpi_jobs) + 1))
    return tmp_mpi_jobs


def random_grouping(n):
    lst: list = deepcopy(nodes)
    random.shuffle(lst)
    groupd_list = [lst[i:i + n] for i in range(0, len(lst), n)]

    if remainder := n - len(groupd_list[-1]) > 0:
        choice_list = list(set(lst) - set(groupd_list[-1]))
        groupd_list[-1].extend(random.choices(choice_list, k=remainder))

    return groupd_list


def run_nccl_test_split_by_node_num(num):
    groups = random_grouping(num)
    return run_group_nccl_test(groups)


def get_bandwidth(logs: str) -> str:
    for log in logs.split("\n"):
        if "Avg bus bandwidth" in log:
            return log.split(":")[-1].strip()
    return "N/A"


def get_summary(node_mpis) -> str:
    print("Summary:")
    result = "\n"
    result += "| nodes | status | bandwidth |\n"
    result += "|-------|--------|-----------|\n"
    for node_mpi in node_mpis:
        result += f"|{','.join(node_mpi.nodes)}|{node_mpi.status:<10}|{get_bandwidth(node_mpi.logs):<8}|\n"
    result += "\n"
    print(result)
    return result


def cleanup():
    # remove k8s yaml dir
    run_cmd(f"rm -rf {k8s_yaml_dir}")
    for need_clean_up_mpi_job in need_clean_up_jobs:
        try:
            need_clean_up_mpi_job.delete_mpi_nccl_job()
        except Exception as e:
            traceback.print_exc()
            print(f"Failed to delete mpi job {need_clean_up_mpi_job.job_name}: {e}")
    print("All jobs cleaned up.")


def run_nccl_test():
    if len(nodes) < 1:
        print("The number of nodes must be greater than 0.")
        exit(0)
    create_nccl_test_namespace()
    create_hostdev()
    with open(test_report_path, "w", encoding="utf-8") as f:
        f.write("# AICP Nccl test report\n")
        f.write("Nodes: %s\n" % ",".join(nodes))

        f.write("## Summary\n")
        step = 1
        if args.mode.isdigit():
            mpi_job_group = run_nccl_test_split_by_node_num(int(args.mode))
            mpi_jobs.append(mpi_job_group)
            f.write(f"### {step} node test\n")
            f.write(get_summary(mpi_job_group))
        else:
            if args.mode == "random":
                while step < len(nodes):
                    mpi_job_group = run_nccl_test_split_by_node_num(step)
                    mpi_jobs.append(mpi_job_group)
                    f.write(f"### {step} node test\n")
                    f.write(get_summary(mpi_job_group))
                    step *= 2
            step = len(nodes)
            mpi_job_group = run_nccl_test_split_by_node_num(len(nodes))
            mpi_jobs.append(mpi_job_group)
            f.write(f"### {step} node test\n")
            f.write(get_summary(mpi_job_group))

        f.write(f"# Detailed report\n")
        for jobs in mpi_jobs:
            f.write("## %d node test\n" % len(jobs[0].nodes))
            for job in jobs:
                f.write(f"### Job {job.job_name}\n")
                f.write(f"Nodes: {job.nodes}\n")
                f.write(f"Logs:\n{job.logs}\n")
                f.write("\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="AICP Nccl test program with container.",
        usage="%(prog)s [options]",
    )

    parser.add_argument("--gpu_num", metavar="gpu_num", type=int, help="the number of gpus to run the test")
    parser.add_argument("--ib_num", metavar="ib_num", type=int, help="the number of ibs to run the test")
    parser.add_argument('--nodes', metavar='nodes', type=str, nargs='+',
                        help='the list of nodes to run the test, eg: --node hs[01-07] hs09')
    parser.add_argument('--image', metavar='image', type=str,
                        default='dockerhub.aicp.local/aicp-common/aicp_common/nccl_test:v3.0',
                        help='the image to run the test')
    parser.add_argument('--reserved', metavar='reserved', type=bool, default=False,
                        help='reserve the test job, default to clean up the job after test.')
    parser.add_argument('--mode', metavar='mode', type=str, default="random",
                        help='test mode, random or full, the default is random. random: will test 1,2,4,...,n nodes, full: will test all nodes only.'
                             'or input nums[<len(nodes)], will split nodes by the num and test each group.')

    parser.add_argument('--NCCL_NVLS_ENABLE', metavar='NCCL_NVLS_ENABLE', type=str, default="1", required=False, choices=["0", "1"],
                        help='the NCCL_NVLS_ENABLE value, default to 1')
    parser.add_argument('--NCCL_NET_GDR_LEVEL', metavar="NCCL_NET_GDR_LEVEL", type=str, default="2", required=False,
                        help="NCCL_NET_GDR_LEVEL, default to 2")
    parser.add_argument('--NCCL_IB_QPS_PER_CONNECTION', metavar="NCCL_IB_QPS_PER_CONNECTION", type=str, default="2", required=False,
                        help="NCCL_IB_QPS_PER_CONNECTION, it can affect performance.")
    parser.add_argument(
        "--NCCL_ALGO",
        metavar="NCCL_ALGO",
        type=str,
        default="Tree,Ring,CollnetDirect,CollnetChain,NVLS,NVLSTree",
        help=(
            "NCCL_ALGO specifies the algorithm to use for NCCL communication. Options are:\n"
            " - Tree: Uses a tree topology for operations like broadcast and reduce, reducing latency in hierarchical networks.\n"
            " - Ring: Uses a ring topology, ideal for all-reduce operations with good scalability and efficient data transfer.\n"
            " - CollnetDirect: Utilizes collective network for direct communication, optimizing performance on systems with NVLink.\n"
            " - CollnetChain: Employs a chain topology in collective network, balancing load and optimizing certain communication patterns.\n"
            " - NVLS: Leverages NVLink/Switch (NVLS) technology for high bandwidth and low latency communication on compatible systems.\n"
            " - NVLSTree: Combines NVLink/Switch technology with a tree topology for efficient broadcast and reduce operations in multi-GPU setups."
        ),
    )
    parser.add_argument("--enable_sharp", action='store_true', help="Enable SHARP for NCCL test")
    args = parser.parse_args()
    nodes = expand_nodes(args.nodes)
    try:
        run_nccl_test()
        print(f"Test report: {test_report_path}")
        print("Done.")
    except Exception as e:
        print(f"Failed to run nccl test: {e}")
    finally:
        if not args.reserved:
            cleanup()