#!/bin/bash

#综合网络命名空间检查脚本
#功能：
#1. 检查宿主机上Pod与netns的对应关系
#2. 检测netns泄漏(特别关注100.7网段)
#3. 检查SANDBOX_NOTREADY状态的Pod
#4. 比较job相关容器的运行状态与Pod状态
#退出码说明：
#0 - 正常
#1 - 命令缺失或crictl配置错误
#2 - 发现网络命名空间泄漏(包含100.7网段)
#3 - 发现SANDBOX_NOTREADY状态的Pod
#4 - 发现容器与Pod状态不一致的问题

set -eo pipefail

#定义颜色代码
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

#初始化异常标志
EXIT_CODE=0

#检查必要的命令是否存在
check_commands() {
    local commands=("jq" "crictl" "ip" "find" "comm" "awk" "grep" "sort")

    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${RED}错误: 未找到命令 '$cmd'，请先安装${NC}" >&2
            exit 1
        fi
    done
}

#获取所有Pod的信息
get_pods_info() {
    if ! crictl pods -o json 2>/dev/null | jq -c '.items[] | {pod_id: .id, pod_name: .metadata.name, namespace: .metadata.namespace, sandbox_id: .sandbox_id, state: .state}' 2>/dev/null; then
        echo -e "${RED}[ERROR] 无法获取Pod信息，请检查crictl配置${NC}" >&2
        exit 1
    fi
}

#获取所有网络命名空间
get_all_netns() {
    ip netns list | awk '{print $1}' 2>/dev/null
}

#通过crictl获取Pod的网络命名空间信息
get_pod_netns_from_cni() {
    local pod_id=$1
    [ -z "$pod_id" ] && { echo ""; return; }

    local inspect_output
    if ! inspect_output=$(crictl inspectp "$pod_id" 2>/dev/null); then
        echo ""
        return
    fi

    local netns=$(echo "$inspect_output" | jq -r '.info.runtimeSpec.linux.namespaces[] | select(.type=="network") | .path' 2>/dev/null)
    [ -n "$netns" ] && { echo $(basename "$netns"); return; }

    local netns_path=$(echo "$inspect_output" | jq -r '.info.netnsPath' 2>/dev/null)
    [ -n "$netns_path" ] && { echo $(basename "$netns_path"); return; }

    echo ""
}

#检查是否是job相关的Pod
is_job_pod() {
    local pod_name=$1
    [[ "$pod_name" == job-* ]]
}

#检查网络命名空间是否包含100.7网段
check_netns_contains_100_7() {
    local netns=$1
    if ip netns exec "$netns" ip a 2>/dev/null | grep -q "100\.7"; then
        return 0
    else
        return 1
    fi
}

#检查容器与Pod状态一致性
#check_container_pod_consistency() {
#    local container_ids=$(crictl ps -a | grep 'job-' | awk '{print $9}' | uniq | sort)
#    local pod_ids=$(crictl pods | grep 'job-' | awk '{print $1}' | uniq | sort)
#
#    local container_file=$(mktemp)
#    local pod_file=$(mktemp)
#    local only_container_file=$(mktemp)
#    local only_pod_file=$(mktemp)
#
#    echo "$container_ids" > "$container_file"
#    echo "$pod_ids" > "$pod_file"
#
#    comm -23 "$container_file" "$pod_file" > "$only_container_file"
#    comm -13 "$container_file" "$pod_file" > "$only_pod_file"
#
#    local only_container_count=$(wc -l < "$only_container_file" | tr -d ' ')
#    local only_pod_count=$(wc -l < "$only_pod_file" | tr -d ' ')
#
#    if [ "$only_container_count" -gt 0 ] || [ "$only_pod_count" -gt 0 ]; then
#        echo -e "\n${RED}=== 发现容器与Pod状态不一致的情况 ===${NC}"
#
#        if [ "$only_container_count" -gt 0 ]; then
#            echo -e "${YELLOW}以下容器在运行但对应的Pod已不存在:${NC}"
#            while read -r id; do
#                [ -z "$id" ] && continue
#                local container_info=$(crictl inspect "$id" 2>/dev/null)
#                if [ $? -eq 0 ]; then
#                    echo "$container_info" | jq -r '.status | "  - \(.id): \(.state) \(.labels["io.kubernetes.pod.name"] // "unknown")"'
#                else
#                    echo "  - $id: 获取容器信息失败"
#                fi
#            done < "$only_container_file"
#        fi
#
#        if [ "$only_pod_count" -gt 0 ]; then
#            echo -e "${YELLOW}以下Pod存在但没有对应的运行中容器:${NC}"
#            while read -r id; do
#                [ -z "$id" ] && continue
#                local pod_info=$(crictl inspectp "$id" 2>/dev/null)
#                if [ $? -eq 0 ]; then
#                    echo "$pod_info" | jq -r '.status | "  - \(.id): \(.state) \(.metadata.name // "unknown")"'
#                else
#                    echo "  - $id: 获取Pod信息失败"
#                fi
#            done < "$only_pod_file"
#        fi
#
#        EXIT_CODE=4
#    fi
#
#    rm -f "$container_file" "$pod_file" "$only_container_file" "$only_pod_file"
#}
check_container_pod_consistency() {
    # 使用JSON格式获取所有job容器ID
    local container_ids=$(crictl ps -a -o json | jq -r '
        .containers[] |
        select(.labels."io.kubernetes.pod.name" | startswith("job-")) |
        .podSandboxId' | sort -u)

    # 使用JSON格式获取所有job Pod ID
    local pod_ids=$(crictl pods -o json | jq -r '
        .items[] |
        select(.metadata.name | startswith("job-")) |
        .id' | sort -u)

    local container_file=$(mktemp)
    local pod_file=$(mktemp)
    local only_container_file=$(mktemp)
    local only_pod_file=$(mktemp)

    echo "$container_ids" > "$container_file"
    echo "$pod_ids" > "$pod_file"

    comm -23 "$container_file" "$pod_file" > "$only_container_file"
    comm -13 "$container_file" "$pod_file" > "$only_pod_file"

    local only_container_count=$(wc -l < "$only_container_file" | tr -d ' ')
    local only_pod_count=$(wc -l < "$only_pod_file" | tr -d ' ')

    if [ "$only_container_count" -gt 0 ] || [ "$only_pod_count" -gt 0 ]; then
        echo -e "\n${RED}=== 发现容器与Pod状态不一致的情况 ===${NC}"

        if [ "$only_container_count" -gt 0 ]; then
            echo -e "${YELLOW}以下容器在运行但对应的Pod已不存在:${NC}"
            while read -r id; do
                [ -z "$id" ] && continue
                local container_info=$(crictl inspect "$id" 2>/dev/null)
                if [ $? -eq 0 ]; then
                    local pod_name=$(echo "$container_info" | jq -r '.labels."io.kubernetes.pod.name" // empty')
                    local state=$(echo "$container_info" | jq -r '.state')
                    echo -e "  - ${RED}$id${NC} (状态: $state, Pod: ${pod_name:-未知})"
                else
                    echo -e "  - ${RED}$id${NC}: 获取容器信息失败"
                fi
            done < "$only_container_file"
        fi

        if [ "$only_pod_count" -gt 0 ]; then
            echo -e "${YELLOW}以下Pod存在但没有对应的运行中容器:${NC}"
            while read -r id; do
                [ -z "$id" ] && continue
                local pod_info=$(crictl inspectp "$id" 2>/dev/null)
                if [ $? -eq 0 ]; then
                    local pod_name=$(echo "$pod_info" | jq -r '.metadata.name // empty')
                    local state=$(echo "$pod_info" | jq -r '.state')
                    echo -e "  - ${RED}$id${NC} (状态: $state, 名称: ${pod_name:-未知})"
                else
                    echo -e "  - ${RED}$id${NC}: 获取Pod信息失败"
                fi
            done < "$only_pod_file"
        fi

        EXIT_CODE=4
    fi

    rm -f "$container_file" "$pod_file" "$only_container_file" "$only_pod_file"
}
#检查SANDBOX_NOTREADY状态的Pod
#check_notready_pods() {
#    local notready_pods_file=$(mktemp)
#
#    get_pods_info | while read -r pod_line; do
#        [ -z "$pod_line" ] && continue
#
#        local pod_id=$(echo "$pod_line" | jq -r '.pod_id')
#        local pod_name=$(echo "$pod_line" | jq -r '.pod_name')
#        local namespace=$(echo "$pod_line" | jq -r '.namespace')
#        local sandbox_id=$(echo "$pod_line" | jq -r '.sandbox_id')
#        local state=$(echo "$pod_line" | jq -r '.state')
#        local netns=$(get_pod_netns_from_cni "$pod_id" "$sandbox_id")
#
#        if [ "$state" = "SANDBOX_NOTREADY" ] && is_job_pod "$pod_name"; then
#            if [ -z "$netns" ]; then
#                echo "${pod_name} (${namespace}) [无netns]"
#            else
#                echo "${pod_name} (${namespace}) [netns: ${netns}]"
#            fi
#        fi
#    done > "$notready_pods_file"
#
#    local notready_count=$(wc -l < "$notready_pods_file" | tr -d ' ')
#
#    if [ "$notready_count" -gt 0 ]; then
#        echo -e "\n${RED}=== 发现 ${notready_count} 个 SANDBOX_NOTREADY 状态的 Job Pod ===${NC}"
#        while IFS= read -r notready_pod; do
#            echo -e "${RED}  - ${notready_pod}${NC}"
#        done < "$notready_pods_file"
#        echo -e "${YELLOW}这些 Job Pod 可能已经异常退出但未清理网络命名空间，建议检查并手动清理！${NC}"
#
#        echo -e "\n${YELLOW}清理建议:${NC}"
#        echo -e "1. 首先确认这些 Job Pod 是否确实不需要了"
#        echo -e "2. 如果确认可以清理，执行以下命令:"
#        while IFS= read -r notready_pod; do
#            if [[ "$notready_pod" == *"[netns:"* ]]; then
#                local netns=$(echo "$notready_pod" | grep -oP '\[netns: \K[^]]+')
#                echo -e "   - 清理网络命名空间: ${YELLOW}ip netns delete ${netns}${NC}"
#            fi
#        done < "$notready_pods_file"
#
#        EXIT_CODE=3
#    fi
#
#    rm -f "$notready_pods_file"
#}
#检查SANDBOX_NOTREADY状态的Pod
check_notready_pods() {
    local notready_pods_file=$(mktemp)
    local has_critical_issue=0

    get_pods_info | while read -r pod_line; do
        [ -z "$pod_line" ] && continue

        local pod_id=$(echo "$pod_line" | jq -r '.pod_id')
        local pod_name=$(echo "$pod_line" | jq -r '.pod_name')
        local namespace=$(echo "$pod_line" | jq -r '.namespace')
        local sandbox_id=$(echo "$pod_line" | jq -r '.sandbox_id')
        local state=$(echo "$pod_line" | jq -r '.state')
        local netns=$(get_pod_netns_from_cni "$pod_id" "$sandbox_id")

        if [ "$state" = "SANDBOX_NOTREADY" ] && is_job_pod "$pod_name"; then
            local severity="WARNING"
            local color="$YELLOW"

            # 检查netns是否包含100.7网段
            if [ -n "$netns" ] && check_netns_contains_100_7 "$netns"; then
                severity="CRITICAL"
                color="$RED"
                has_critical_issue=1
            fi

            if [ -z "$netns" ]; then
                echo "[$severity] ${pod_name} (${namespace}) [无netns]"
            else
                echo "[$severity] ${pod_name} (${namespace}) [netns: ${netns}]"
                if [ "$severity" = "CRITICAL" ]; then
                    echo "  ${RED}>> 包含100.7网段IP:$(ip netns exec "$netns" ip a | grep -oP '100\.7\.[0-9.]+' | tr '\n' ' ')${NC}"
                fi
            fi
        fi
    done > "$notready_pods_file"

    local notready_count=$(wc -l < "$notready_pods_file" | tr -d ' ')

    if [ "$notready_count" -gt 0 ]; then
        echo -e "\n${YELLOW}=== 发现 ${notready_count} 个 SANDBOX_NOTREADY 状态的 Job Pod ===${NC}"

        # 输出所有问题Pod
        while IFS= read -r notready_pod; do
            if [[ "$notready_pod" == *"[CRITICAL]"* ]]; then
                echo -e "${RED}$notready_pod${NC}"
            else
                echo -e "${YELLOW}$notready_pod${NC}"
            fi
        done < "$notready_pods_file"

        # 根据严重程度设置退出码
        if [ "$has_critical_issue" -eq 1 ]; then
            echo -e "${RED}发现包含100.7网段的异常Pod，需要立即处理！${NC}"
            EXIT_CODE=3
        else
            echo -e "${YELLOW}这些Job Pod可能已经异常退出,但netns并不包含100网段ip，建议检查${NC}"
            # 如果没有其他错误，只设置警告状态(不覆盖更严重的错误)
            [ $EXIT_CODE -eq 0 ] && EXIT_CODE=1
        fi

        echo -e "\n${BLUE}清理建议:${NC}"
        echo -e "1. 首先确认这些Pod是否确实不需要了"
        echo -e "2. 如果确认可以清理，执行以下命令:"
        while IFS= read -r notready_pod; do
            if [[ "$notready_pod" == *"[netns:"* ]]; then
                local netns=$(echo "$notready_pod" | grep -oP '\[netns: \K[^]]+')
                echo -e "   - 清理网络命名空间: ${YELLOW}ip netns delete ${netns}${NC}"
            fi
        done < "$notready_pods_file"
    fi

    rm -f "$notready_pods_file"
}
#主检查函数
check_netns_leak() {
    local all_netns_file=$(mktemp)
    local used_netns_file=$(mktemp)
    local leaked_netns_file=$(mktemp)

    get_all_netns > "$all_netns_file"
    get_pods_info | while read -r pod_line; do
        [ -z "$pod_line" ] && continue
        local pod_id=$(echo "$pod_line" | jq -r '.pod_id')
        local sandbox_id=$(echo "$pod_line" | jq -r '.sandbox_id')
        local netns=$(get_pod_netns_from_cni "$pod_id" "$sandbox_id")
        [ -n "$netns" ] && echo "$netns"
    done | sort -u > "$used_netns_file"

    comm -23 <(sort "$all_netns_file") <(sort "$used_netns_file") > "$leaked_netns_file"
    local leaked_count=$(wc -l < "$leaked_netns_file" | tr -d ' ')

    if [ "$leaked_count" -gt 0 ]; then
        local confirmed_leaks=0
        local leak_details=""

        while IFS= read -r netns; do
            if check_netns_contains_100_7 "$netns"; then
                leak_details+="\n${RED}[泄漏确认] ${netns} 包含100.7网段${NC}"
                leak_details+="\n$(ip netns exec "$netns" ip a | grep -E "inet|100\.7" | sed 's/^/  /')"
                ((confirmed_leaks++))
            fi
        done < "$leaked_netns_file"

        if [ "$confirmed_leaks" -gt 0 ]; then
            echo -e "\n${RED}=== 发现 ${confirmed_leaks} 个确认泄漏的网络命名空间(包含100.7网段) ===${NC}"
            echo -e "$leak_details"
            EXIT_CODE=2
        fi
    fi

    rm -f "$all_netns_file" "$used_netns_file" "$leaked_netns_file"
}

#主函数
main() {
    check_commands

    # 执行所有检查
    check_netns_leak
    check_notready_pods
    check_container_pod_consistency

# 如果没有发现任何问题，返回成功
#if [ $EXIT_CODE -eq 0 ]; then
#echo -e "${GREEN}所有检查正常，未发现异常${NC}"
#fi

    exit $EXIT_CODE
}

#执行主函数
main