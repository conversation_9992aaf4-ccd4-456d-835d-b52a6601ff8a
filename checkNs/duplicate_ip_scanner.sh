#!/bin/bash

# 功能：
# 1. 检查所有主机的网络命名空间
# 2. 找出重复的100.7网段IP
# 3. 统计这些IP在不同主机上的分布情况

# 日志配置
LOG_FILE="./netns_check_$(date +%Y%m%d_%H%M%S).log"
RESULT_FILE="./netns_check_result_$(date +%Y%m%d_%H%M%S).log"

# 清理3天前日志
find "." -name "netns_check_*.log" -mtime +3 -delete 2>/dev/null

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 主检查函数
check_namespaces() {
    local host=$1
    echo -e "${YELLOW}检查主机: $host${NC}"

    # 获取所有网络命名空间及其IP
    local output
    output=$(ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no "$host" \
        'ip netns list | awk '\''{print $1}'\'' | while read -r ns; do \
            echo "Namespace: $ns"; \
            ip netns exec "$ns" ip a | grep 100.7 | awk -F'\''[ /]+'\'' '\''{print $3}'\''; \
        done' 2>&1)

    # 检查SSH连接是否成功
    local ssh_exit_code=$?
    if [ $ssh_exit_code -ne 0 ]; then
        echo -e "${RED}SSH连接失败: $host${NC}"
        echo "$host SSH连接失败 (退出码: $ssh_exit_code)" >> "$LOG_FILE"
        echo "$output" >> "$LOG_FILE"
        echo "----------------------------------------" >> "$LOG_FILE"
        return 1
    fi

    # 记录日志
    echo "$host 检查结果:" >> "$LOG_FILE"
    echo "$output" >> "$LOG_FILE"
    echo "----------------------------------------" >> "$LOG_FILE"

    # 提取IP和对应的namespace并记录
    local current_ns=""
    echo "$output" | while read -r line; do
        if [[ $line == Namespace:* ]]; then
            current_ns=$(echo "$line" | awk '{print $2}')
        elif [ -n "$line" ] && [[ $line =~ ^100\.7 ]]; then
            # 格式：IP,主机,命名空间
            echo "\"$line\",\"$host\",\"$current_ns\"" >> "$RESULT_FILE"
        fi
    done
}


# 分析结果
analyze_results() {
    echo -e "\n${GREEN}分析结果:${NC}"

    # 检查结果文件是否存在且不为空
    if [ ! -s "$RESULT_FILE" ]; then
        echo -e "${YELLOW}没有找到任何100.7网段的IP地址${NC}"
        return
    fi

    # 提取所有100.7开头的IP、主机和命名空间
    grep -E '"100\.7[^"]*","[^"]+","[^"]+"' "$RESULT_FILE" > /tmp/1007_ips.tmp

    if [ ! -s /tmp/1007_ips.tmp ]; then
        echo -e "${YELLOW}没有找到任何100.7网段的IP地址${NC}"
        rm -f /tmp/1007_ips.tmp
        return
    fi

    # 找出重复的IP
    awk -F'[",]+' '{print $2}' /tmp/1007_ips.tmp | sort | uniq -c | sort -nr | awk '$1 > 1 {print $2}' > /tmp/duplicate_ips.tmp

    if [ ! -s /tmp/duplicate_ips.tmp ]; then
        echo -e "${GREEN}没有发现重复的IP地址${NC}"
        echo -e "${GREEN}所有100.7网段IP地址分布:${NC}"
        while IFS=',' read -r ip host ns; do
            # 去除引号
            ip=$(echo "$ip" | tr -d '"')
            host=$(echo "$host" | tr -d '"')
            ns=$(echo "$ns" | tr -d '"')
            echo -e "  IP: ${YELLOW}$ip${NC} -> 主机: ${GREEN}$host${NC} -> 命名空间: ${GREEN}$ns${NC}"
        done < /tmp/1007_ips.tmp
    else
        # 为每个重复IP显示分布的主机和命名空间
        while read -r ip; do
            echo -e "\n${RED}重复IP: $ip${NC}"
            grep "\"$ip\"" "$RESULT_FILE" | while IFS=',' read -r found_ip host ns; do
                # 去除引号
                found_ip=$(echo "$found_ip" | tr -d '"')
                host=$(echo "$host" | tr -d '"')
                ns=$(echo "$ns" | tr -d '"')
                echo -e "  -> 主机: ${YELLOW}$host${NC} | 命名空间: ${YELLOW}$ns${NC}"
            done
        done < /tmp/duplicate_ips.tmp
    fi

    # 清理临时文件
    rm -f /tmp/1007_ips.tmp /tmp/duplicate_ips.tmp
}


# 显示SSH连接失败的主机
show_failed_connections() {
    local failed_hosts=$(grep -c "SSH连接失败" "$LOG_FILE" 2>/dev/null || echo "0")

    if [ $failed_hosts -gt 0 ]; then
        echo -e "\n${RED}SSH连接失败的主机 ($failed_hosts 台):${NC}"
        grep -B1 "SSH连接失败" "$LOG_FILE" | grep -v "SSH连接失败" | grep -v "^--$" | while read -r line; do
            host=$(echo "$line" | awk '{print $1}')
            echo -e "  - ${RED}$host${NC}"
        done
    fi
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        echo -e "${RED}用法: $0 <主机列表文件>${NC}"
        exit 1
    fi

    local hostfile=$1

    # 检查主机列表文件
    if [ ! -f "$hostfile" ]; then
        echo -e "${RED}错误: 主机列表文件 $hostfile 不存在${NC}"
        exit 1
    fi

    # 统计总主机数
    local total_hosts=$(grep -c "." "$hostfile")
    echo -e "${GREEN}开始网络命名空间检查... (共 $total_hosts 台主机)${NC}"

    # 初始化日志文件
    echo "网络命名空间检查开始 - $(date)" > "$LOG_FILE"
    echo "主机列表文件: $hostfile" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"

    # 并行检查所有主机
    while read -r host; do
        [ -z "$host" ] && continue
        check_namespaces "$host" &
    done < "$hostfile"

    wait

    # 显示SSH连接失败的主机
    show_failed_connections

    # 分析结果
    analyze_results

    echo -e "\n${GREEN}检查完成!${NC}"
    echo -e "日志文件: $LOG_FILE"
    echo -e "结果文件: $RESULT_FILE"
}

# 执行主函数
main "$@"