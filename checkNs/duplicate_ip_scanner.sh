#!/bin/bash

# 功能：
# 1. 检查所有主机的网络命名空间
# 2. 找出重复的100.7网段IP
# 3. 统计这些IP在不同主机上的分布情况

# 日志配置
LOG_FILE="./netns_check_$(date +%Y%m%d_%H%M%S).log"
RESULT_FILE="./netns_check_result_$(date +%Y%m%d_%H%M%S).log"

# 清理3天前日志
find "$LOG_DIR" -name "*.log" -mtime +3 -delete 2>/dev/null

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 主检查函数
check_namespaces() {
    local host=$1
    echo -e "${YELLOW}检查主机: $host${NC}"

    # 获取所有网络命名空间及其IP
    local output
    output=$(ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no "$host" \
        'ip netns list | awk '\''{print $1}'\'' | while read -r ns; do \
            echo "Namespace: $ns"; \
            ip netns exec "$ns" ip a | grep 100.7 | awk -F'\''[ /]+'\'' '\''{print $3}'\''; \
        done' 2>&1)

    # 记录日志
    echo "$host 检查结果:" >> "$LOG_FILE"
    echo "$output" >> "$LOG_FILE"
    echo "----------------------------------------" >> "$LOG_FILE"

    # 提取IP并记录
    echo "$output" | grep -v "Namespace:" | while read -r ip; do
        if [ -n "$ip" ]; then
            echo "\"$ip\",\"$host\"" >> "$RESULT_FILE"
        fi
    done
}


# 分析结果
analyze_results() {
    echo -e "\n${GREEN}分析结果:${NC}"

    # 提取所有100.7开头的IP和主机对
    grep -E '"100\.7[^"]*","[^"]+"' "$RESULT_FILE" > /tmp/1007_ips.tmp

    # 找出重复的IP
    awk -F'[",]+' '{print $2}' /tmp/1007_ips.tmp | sort | uniq -c | sort -nr | awk '$1 > 1 {print $2}' > /tmp/duplicate_ips.tmp

    # 为每个重复IP显示分布的主机
    while read -r ip; do
        echo -e "\n${RED}重复IP: $ip${NC}"
        grep "\"$ip\"" "$RESULT_FILE"
    done < /tmp/duplicate_ips.tmp


    # 清理临时文件
    rm -f /tmp/1007_ips.tmp /tmp/duplicate_ips.tmp
}


# 主函数
main() {
    # 检查参数
    if [ $# -eq  ]; then
        echo -e "${RED}用法: $0 <主机列表文件>${NC}"
        exit 1
    fi

    local hostfile=$1

    # 检查主机列表文件
    if [ ! -f "$hostfile" ]; then
        echo -e "${RED}错误: 主机列表文件 $hostfile 不存在${NC}"
        exit 1
    fi

    # 开始检查
    echo -e "${GREEN}开始网络命名空间检查...${NC}"

    # 并行检查所有主机
    while read -r host; do
        [ -z "$host" ] && continue
        check_namespaces "$host" &
    done < "$hostfile"

    wait

    # 分析结果
    analyze_results
    echo -e "日志文件: $LOG_FILE"
    echo -e "结果文件: $RESULT_FILE"
}

# 执行主函数
main "$@"