FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg2 \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# 安装Python依赖
RUN pip install pyyaml

# 复制脚本
COPY nccl-test.py /app/nccl-test.py
COPY host_dev.yaml /app/host_dev.yaml

WORKDIR /app

# 设置入口点
ENTRYPOINT ["python", "nccl-test.py"] 