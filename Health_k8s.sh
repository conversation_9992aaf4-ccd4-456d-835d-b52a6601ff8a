#!/usr/bin/env bash

SYSTEM_NS="kube-system"
ERRORS=0
LOG_LEVEL=${LOG_LEVEL:-"INFO"} # 可设置为 DEBUG, INFO, WARN, ERROR

# SSH options
SSH_OPTS="-o BatchMode=yes -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=5"

# 颜色定义
RED="\033[0;31m"; GREEN="\033[0;32m"; YELLOW="\033[1;33m"
BLUE="\033[0;34m"; MAGENTA="\033[0;35m"; CYAN="\033[0;36m"; NC="\033[0m"

# 日志级别函数
should_log() {
  local level=$1
  declare -A levels=([DEBUG]=0 [INFO]=1 [WARN]=2 [ERROR]=3)
  [[ ${levels[$level]} -ge ${levels[$LOG_LEVEL]} ]]
}

log() {
  should_log "INFO" && echo -e "${GREEN}[INFO]${NC} $1"
}

debug() {
  should_log "DEBUG" && echo -e "${BLUE}[DEBUG]${NC} $1"
}

warn() {
  should_log "WARN" && echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
  should_log "ERROR" && echo -e "${RED}[ERROR]${NC} $1"
  ((ERRORS++))
}

success() {
  should_log "INFO" && echo -e "${GREEN}✓${NC} $1"
}

section() {
  should_log "INFO" && echo -e "\n${MAGENTA}=== $1 ===${NC}"
}

check_control_plane() {
  section "Control Plane 检查"
  local errors=0

  debug "获取Kubernetes组件状态..."
  mapfile -t comps < <(kubectl get componentstatuses --no-headers 2>/dev/null)

  for line in "${comps[@]}"; do
    name=$(awk '{print $1}' <<<"$line")
    status=$(awk '{print $2}' <<<"$line")
    if [[ "$status" != "Healthy" ]]; then
      error "组件 $name 状态异常: $status"
      ((errors++))
    else
      debug "组件 $name 状态正常"
    fi
  done

  debug "检查API Server健康端点..."
  for ep in healthz readyz; do
    if kubectl get --raw /$ep --request-timeout=5s &>/dev/null; then
      debug "API Server /$ep 端点正常"
    else
      error "API Server /$ep 端点不可达"
      ((errors++))
    fi
  done

  if [[ $errors -eq 0 ]]; then
    success "Control Plane 检查通过"
  else
    error "Control Plane 检查发现 $errors 个问题"
  fi
}

check_nodes() {
  section "节点状态检查"
  local errors=0

  debug "获取节点列表..."
  mapfile -t nodes < <(kubectl get nodes -l node-role.kubernetes.io/baai-node= -o name)

  for nodeRes in "${nodes[@]}"; do
    node=${nodeRes#node/}
    ready=$(kubectl get $nodeRes -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
    if [[ "$ready" != "True" ]]; then
      error "节点 $node 未就绪"
      ((errors++))
    else
      debug "节点 $node 状态正常"
    fi
  done

  if [[ $errors -eq 0 ]]; then
    success "节点状态检查通过"
  else
    error "节点状态检查发现 $errors 台机器存在问题"
  fi
}

#check_daemonset_health() {
#  local name=$1
#  local namespace=$2
#  local label_selector=$3
#
#  section "检查 DaemonSet: $name"
#  local errors=0
#
#  debug "获取DaemonSet状态..."
#  ds_status=$(kubectl get ds "$name" -n "$namespace" -o=jsonpath='{.status.desiredNumberScheduled}/{.status.numberReady}' 2>/dev/null)
#
#  if [[ -z "$ds_status" ]]; then
#    error "DaemonSet $name 未找到"
#    return 1
#  fi
#
#  desired=${ds_status%%/*}
#  ready=${ds_status##*/}
#
#  if [[ "$desired" != "$ready" ]]; then
#    error "DaemonSet $name 未完全就绪 (期望: $desired, 实际: $ready)"
#    ((errors++))
#  else
#    debug "DaemonSet $name 已完全就绪"
#  fi
#
#  debug "检查关联Pod状态..."
#  mapfile -t lines < <(kubectl get pods -n "$namespace" -l "$label_selector" --no-headers 2>/dev/null)
#
#  for line in "${lines[@]}"; do
#    pod_name=$(awk '{print $1}' <<< "$line")
#    ready_col=$(awk '{print $2}' <<< "$line")
#    status_col=$(awk '{print $3}' <<< "$line")
#
#    if [[ "$status_col" != "Running" || "$ready_col" != */* ]]; then
#      error "Pod $pod_name 状态异常: READY=$ready_col STATUS=$status_col"
#      ((errors++))
#    else
#      ready_num=${ready_col%%/*}
#      total_num=${ready_col##*/}
#      if [[ "$ready_num" != "$total_num" ]]; then
#        error "Pod $pod_name 容器未完全就绪: READY=$ready_col"
#        ((errors++))
#      else
#        debug "Pod $pod_name 状态正常"
#      fi
#    fi
#  done
#
#  if [[ $errors -eq 0 ]]; then
#    success "DaemonSet $name 检查通过"
#  else
#    error "DaemonSet $name 检查发现 $errors 个问题"
#  fi
#}

check_daemonset_health() {
  local name=$1
  local namespace=$2
  local label_selector=$3
  local node_label_selector=$4  # 新增：传入节点的 label 筛选

  section "检查 DaemonSet: $name"
  local errors=0

  debug "获取DaemonSet状态..."
  ds_status=$(kubectl get ds "$name" -n "$namespace" -o=jsonpath='{.status.desiredNumberScheduled}/{.status.numberReady}' 2>/dev/null)

  if [[ -z "$ds_status" ]]; then
    error "DaemonSet $name 未找到"
    return 1
  fi

  desired=${ds_status%%/*}
  ready=${ds_status##*/}

  if [[ "$desired" != "$ready" ]]; then
    error "DaemonSet $name 未完全就绪 (期望: $desired, 实际: $ready)"
    ((errors++))

    # 排查缺失 Pod 的节点，支持 label 筛选
    debug "排查缺失 Pod 的节点..."

    # 获取符合 label 筛选条件的 Ready 节点
    mapfile -t ready_nodes < <(kubectl get nodes -l "$node_label_selector" --no-headers | awk '$2 == "Ready" {print $1}')

    # 获取该 DaemonSet 的 Pod 所在节点
    mapfile -t ds_pods_nodes < <(kubectl get pods -n "$namespace" -l "$label_selector" -o=jsonpath='{range .items[*]}{.spec.nodeName}{"\n"}{end}' 2>/dev/null)

    # 构造集合判断
    for node in "${ready_nodes[@]}"; do
      if ! printf "%s\n" "${ds_pods_nodes[@]}" | grep -qx "$node"; then
        warn "节点 $node 状态为 Ready，但未调度到 DaemonSet Pod"
      fi
    done
  else
    debug "DaemonSet $name 已完全就绪"
  fi

  debug "检查关联Pod状态..."
  mapfile -t lines < <(kubectl get pods -n "$namespace" -l "$label_selector" --no-headers 2>/dev/null)

  for line in "${lines[@]}"; do
    pod_name=$(awk '{print $1}' <<< "$line")
    ready_col=$(awk '{print $2}' <<< "$line")
    status_col=$(awk '{print $3}' <<< "$line")

    if [[ "$status_col" != "Running" || "$ready_col" != */* ]]; then
      node_name=$(kubectl get pod "$pod_name" -n "$namespace" -o=jsonpath='{.spec.nodeName}')
      node_ready=$(kubectl get node "$node_name" -o=jsonpath='{.status.conditions[?(@.type=="Ready")].status}')

      if [[ "$node_ready" != "True" ]]; then
        warn "Pod $pod_name 状态异常 (READY=$ready_col STATUS=$status_col)，但所在节点 $node_name 不可用 ($node_ready)，忽略此问题"
        continue
      fi

      error "Pod $pod_name 状态异常: READY=$ready_col STATUS=$status_col"
      ((errors++))
    else
      ready_num=${ready_col%%/*}
      total_num=${ready_col##*/}
      if [[ "$ready_num" != "$total_num" ]]; then
        error "Pod $pod_name 容器未完全就绪: READY=$ready_col"
        ((errors++))
      else
        debug "Pod $pod_name 状态正常"
      fi
    fi
  done

  if [[ $errors -eq 0 ]]; then
    success "DaemonSet $name 检查通过"
  else
    error "DaemonSet $name 检查发现 $errors 个问题"
  fi
}


check_gpu_count() {
  section "GPU数量检查"
  local errors=0
  tmp_dir=$(mktemp -d)
  pids=()
  max_parallel=60
  current_jobs=0

  debug "创建临时目录: $tmp_dir"
  debug "最大并行数: $max_parallel"

  run_check() {
    local node=$1
    {
      debug "检查节点 $node..."
      ready=$(kubectl get node "$node" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
      if [[ "$ready" != "True" ]]; then
        echo "ERROR:Node $node is not Ready, skipping GPU check" > "$tmp_dir/$node"
        exit
      fi

      phys=$(ssh $SSH_OPTS "$node" "nvidia-smi --query-gpu=index --format=csv,noheader" 2>/dev/null | wc -l)
      if [[ $? -ne 0 ]]; then
        echo "ERROR:SSH:$node" > "$tmp_dir/$node"
        exit
      fi

      cap=$(kubectl get node "$node" -o jsonpath='{.status.capacity.nvidia\.com/gpu}' 2>/dev/null)
      if [[ $? -ne 0 ]]; then
        echo "ERROR:K8S:$node" > "$tmp_dir/$node"
        exit
      fi

      echo "$phys/$cap" > "$tmp_dir/$node"
    } &
    pids+=($!)
    ((current_jobs++))

    if [[ "$current_jobs" -ge "$max_parallel" ]]; then
      wait -n
      ((current_jobs--))
    fi
  }

  for nodePath in "${nodes[@]}"; do
    node=${nodePath#node/}
    run_check "$node"
  done

  debug "等待所有检查完成..."
  wait

  for nodePath in "${nodes[@]}"; do
    node=${nodePath#node/}
    result_file="$tmp_dir/$node"

    if [[ ! -f "$result_file" ]]; then
      error "节点 $node: 结果文件缺失"
      ((errors++))
      continue
    fi

    result=$(<"$result_file")
    if [[ "$result" == ERROR:* ]]; then
      case "$result" in
        ERROR:SSH:*) error "节点 $node: SSH连接失败";;
        ERROR:K8S:*) error "节点 $node: Kubernetes API调用失败";;
        ERROR:Node*) error "${result#ERROR:}";;
      esac
      ((errors++))
    else
      phys=${result%%/*}
      cap=${result##*/}
      if [[ "$phys" -ne "$cap" ]]; then
        error "节点 $node: GPU数量不匹配 (物理: $phys, K8s上报: $cap)"
        ((errors++))
      elif [[ "$phys" -ne 8 ]]; then
        error "节点 $node: GPU数量异常 (实际: $phys, 期望: 8)"
        ((errors++))
      else
        debug "节点 $node: GPU数量正常"
      fi
    fi
  done

  rm -rf "$tmp_dir"

  if [[ $errors -eq 0 ]]; then
    success "GPU数量检查通过"
  else
    error "GPU数量检查发现 $errors 台机器存在问题"
  fi
}

check_pods_airs() {
  section "检查 airs 命名空间 Pod"
  local errors=0

  debug "获取 airs 命名空间 Pod 状态..."
  mapfile -t lines < <(kubectl get pods -n airs --no-headers)

  for line in "${lines[@]}"; do
    pod=$(awk '{print $1}' <<< "$line")
    status=$(awk '{print $3}' <<< "$line")

    if [[ "$status" == "Terminating" ]]; then
      error "Pod airs/$pod 处于 Terminating 状态"
      ((errors++))
    else
      debug "Pod airs/$pod 状态正常: $status"
    fi
  done

  if [[ $errors -eq 0 ]]; then
    success "airs Pod 检查通过"
  else
    error "airs Pod 检查发现 $errors 个问题"
  fi
}

check_coredns() {
  echo -e "\n== CoreDNS 状态检查 =="

  # 获取 CoreDNS Pod 状态信息
  mapfile -t pods < <(
    kubectl get pods -A -l k8s-app=kube-dns -o json | jq -r '
      .items[] |
      . as $pod |
      $pod.metadata.name as $name |
      $pod.status.phase as $phase |
      ($pod.status.containerStatuses[0].restartCount // 0) as $restarts |
      "\($name)\t\($phase)\t\($restarts)"
    '
  )

  if [[ ${#pods[@]} -eq 0 ]]; then
    echo "未找到 CoreDNS Pod"
    return
  fi

  for line in "${pods[@]}"; do
    IFS=$'\t' read -r name phase restart <<< "$line"
    printf "Pod: %-40s 状态: %-10s 重启次数: %s\n" "$name" "$phase" "$restart"
  done

  echo -e "\n== 域名解析测试 =="

  # 设置要测试的域名列表
  domain_list=(
    "kubernetes.default.svc.cluster.local"
    "kube-dns.kube-system.svc.cluster.local"
    # 添加更多要测试的域名
  )

  random_pod=$(kubectl get pods -n airs-system --no-headers | grep Running | grep -v kube-system | shuf -n 1)

  if [[ -z "$random_pod" ]]; then
    echo "没有找到合适的运行中 Pod 来执行 DNS 测试"
    return
  fi

#  ns=$(awk '{print $1}' <<< "$random_pod")
  ns="airs-system"
  pod=$(awk '{print $1}' <<< "$random_pod")

  echo "使用 Pod [$pod] (namespace: $ns) 进行 DNS 测试"

  for domain in "${domain_list[@]}"; do
    echo -e "\n域名: $domain"
    kubectl exec -n "$ns" "$pod" -- ping -c 2 "$domain" || echo "❌ 解析失败"
  done
}

check_spider_pool() {
  section "检查 SpiderPool"
  local errors=0

  debug "检查 spiderpool-controller..."
  controller_status=$(kubectl get deploy spiderpool-controller -n kube-system -o jsonpath='{.status.readyReplicas}/{.status.replicas}' 2>/dev/null)

  if [[ "$controller_status" != "3/3" ]]; then
    error "spiderpool-controller 未就绪: $controller_status"
    ((errors++))
  else
    debug "spiderpool-controller 状态正常"
  fi

  debug "检查 spiderpool-agent..."
  agent_status=$(kubectl get ds spiderpool-agent -n kube-system -o jsonpath='{.status.numberReady}/{.status.desiredNumberScheduled}' 2>/dev/null)

  if [[ "$agent_status" != */* ]]; then
    error "获取 spiderpool-agent 状态失败"
    ((errors++))
  else
    ready=${agent_status%%/*}
    desired=${agent_status##*/}
    if [[ "$ready" != "$desired" ]]; then
      error "spiderpool-agent 未完全就绪: $ready/$desired"
      ((errors++))
    else
      debug "spiderpool-agent 状态正常"
    fi
  fi

  debug "检查 spiderpool Pod..."
  mapfile -t bad_spider_pods < <(kubectl get pods -n kube-system -l app.kubernetes.io/name=spiderpool-agent \
    -o json | jq -r '.items[] | select(.status.phase != "Running") | "\(.metadata.name) on node \(.spec.nodeName) is in \(.status.phase) ph
ase"')

  for line in "${bad_spider_pods[@]}"; do
    error "$line"
    ((errors++))
  done

  if [[ $errors -eq 0 ]]; then
    success "SpiderPool 检查通过"
  else
    error "SpiderPool 检查发现 $errors 个问题"
  fi
}

check_oom_pods() {
  local namespaces=("$@")
  section "检查 Pod OOM 情况"

  for ns in "${namespaces[@]}"; do
    debug "检查命名空间: $ns"
    local errors=0

    # 获取 OOMKilled 的容器信息
    mapfile -t pods < <(
      kubectl get pods -n "$ns" -o json | \
      jq -r '
        .items[] |
        . as $pod |
        .spec.nodeName as $node |
        .metadata.name as $podname |
        .status.containerStatuses[]? |
        select(.lastState.terminated.reason == "OOMKilled") |
        "\($podname)\t\($node)\t\(.name)\tOOMKilled\t\(.restartCount)"
      '
    )

    if [[ ${#pods[@]} -eq 0 ]]; then
      success "$ns 命名空间未发现 OOMKilled Pod"
      continue
    fi

    for pod_info in "${pods[@]}"; do
      IFS=$'\t' read -r pod node container reason restarts <<< "$pod_info"
      error "$ns/$pod (节点: $node, 容器: $container) 出现 OOMKilled，重启次数: $restarts"
      ((errors++))
    done

    if [[ $errors -eq 0 ]]; then
      success "$ns 命名空间 OOMKilled 检查通过"
    else
      error "$ns 命名空间发现 $errors 个 OOMKilled Pod"
    fi
  done
}

check_roce_config() {
  section "RoCE 网卡映射检查（airs 命名空间）"
  local errors=0

  local expected_networks="kube-system/macvlan-net1,kube-system/macvlan-net2,kube-system/macvlan-net3,kube-system/macvlan-net4,kube-system/macvlan-net5,kube-system/macvlan-net6,kube-system/macvlan-net7,kube-system/macvlan-net8"

  # 获取所有符合前缀的 Pod
  mapfile -t pods < <(kubectl get pods -n airs --no-headers -o custom-columns=":metadata.name" | grep '^job-')

  for pod in "${pods[@]}"; do
    # 获取状态、nodeName、资源申请
    status=$(kubectl get pod "$pod" -n airs -o jsonpath="{.status.phase}")
    node=$(kubectl get pod "$pod" -n airs -o jsonpath="{.spec.nodeName}")
    rdma_request=$(kubectl get pod "$pod" -n airs -o jsonpath="{.spec.containers[*].resources.requests['rdma/mlnx_shared']}")

    # 只检查 Running 且申请了 rdma/mlnx_shared 的 Pod
    if [[ "$status" != "Running" ]]; then
      debug "跳过 Pod $pod：状态为 $status"
      continue
    fi
    if [[ -z "$rdma_request" ]]; then
      debug "跳过 Pod $pod：未申请 rdma/mlnx_shared 资源"
      continue
    fi

    networks=$(kubectl get pod "$pod" -n airs -o jsonpath="{.metadata.annotations['k8s\.v1\.cni\.cncf\.io/networks']}")
    if [[ "$networks" != "$expected_networks" ]]; then
      error "RoCE 映射异常 - Pod: $pod, Node: $node"
      ((errors++))
    else
      debug "Pod $pod 网络配置正常"
    fi
  done

  if [[ $errors -eq 0 ]]; then
    success "RoCE 网卡映射检查通过"
  else
    error "RoCE 网卡映射检查发现 $errors 个问题"
  fi
}

main() {
  section "开始集群健康检查"
  debug "初始化节点列表..."
  mapfile -t nodes < <(kubectl get nodes -l node-role.kubernetes.io/baai-node= -o name)
  debug "发现 ${#nodes[@]} 个节点"

  check_control_plane
  check_nodes
  check_daemonset_health "nvidia-device-plugin" "$SYSTEM_NS" "app.kubernetes.io/name=nvidia-device-plugin" "machine.baai.ac.cn/accelerator-vendor=NVIDIA"
#  check_daemonset_health "rdma-shared-dp-ds" "$SYSTEM_NS" "app.kubernetes.io/name=spiderpool-agent" "node-role.kubernetes.io/baai-node"
  check_daemonset_health "spiderpool-rdma-shared-device-plugin" "$SYSTEM_NS" "app.kubernetes.io/name=spiderpool" "node-role.kubernetes.io/baai-node"
  check_daemonset_health "node-local-dns" "$SYSTEM_NS" "k8s-app=node-local-dns" "node-role.kubernetes.io/baai-node"
  check_gpu_count
  check_pods_airs
  check_coredns
  check_spider_pool
  # 调用 OOM 检查，传入需要检查的命名空间
  check_oom_pods "kube-system" "airs-system"
  check_roce_config


  section "检查结果汇总"
  if [[ $ERRORS -gt 0 ]]; then
    error "检查完成，共发现 $ERRORS 个问题"
    exit 1
  else
    success "所有检查通过，集群状态健康"
    exit 0
  fi
}

main