import datetime
import json
import os.path
import sys
import time
import traceback
import subprocess
import signal
from copy import deepcopy
import random
import argparse
import re

class DockerNcclTest:
    def __init__(self, nodes, gpu_num, ib_num, image, nccl_config):
        self.nodes = nodes
        self.gpu_num = gpu_num
        self.ib_num = ib_num
        self.image = image
        self.nccl_config = nccl_config
        self.job_name = f"nccl-test-{int(time.time())}"
        self._status = "Running"
        self._completed = False
        self._logs = ""
        self._bandwidth = ""
        self.process = None
        
    def create_docker_command(self):
        """创建Docker运行命令"""
        base_command = [
            "docker", "run", "--rm",
            "--network=host",  # 使用主机网络
            "--privileged",    # 需要特权模式访问GPU和网络设备
        ]
        
        # 添加GPU支持
        if self.gpu_num > 0:
            base_command.extend(["--gpus", f"all"])
            
        # 添加环境变量
        env_vars = [
            f"NCCL_IB_TC=160",
            f"NCCL_IB_QPS_PER_CONNECTION={self.nccl_config.get('NCCL_IB_QPS_PER_CONNECTION', '2')}",
            "NCCL_IB_HCA=mlx5",
            f"NCCL_ALGO={self.nccl_config.get('NCCL_ALGO', 'Tree,Ring')}",
            f"NCCL_NET_GDR_LEVEL={self.nccl_config.get('NCCL_NET_GDR_LEVEL', '2')}",
            "NCCL_DEBUG=INFO",
            "NCCL_IB_DISABLE=0",
            "NCCL_IB_MERGE_VFS=0",
            f"NCCL_NVLS_ENABLE={self.nccl_config.get('NCCL_NVLS_ENABLE', '1')}",
        ]
        
        for env_var in env_vars:
            base_command.extend(["-e", env_var])
            
        # 添加镜像和命令
        base_command.extend([
            self.image,
            "mpirun",
            "--allow-run-as-root",
            "--report-bindings",
            "-N", "1",
            "-n", str(len(self.nodes)),
            "--bind-to", "none",
            "-mca", "btl_base_verbose", "100",
            "-mca", "orte_base_help_aggregate", "0",
            "-mca", "btl_openib_allow_ib", "1",
            "-mca", "btl_openib_warn_default_gid_prefix", "0",
            "/opt/nccl-tests/build/all_reduce_perf",
            "-b", "1G",
            "-e", "1G", 
            "-f", "2",
            "-g", str(self.gpu_num),
        ])
        
        return base_command
        
    def run_test(self):
        """运行NCCL测试"""
        try:
            cmd = self.create_docker_command()
            print(f"Running command: {' '.join(cmd)}")
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时读取输出
            logs = []
            while True:
                output = self.process.stdout.readline()
                if output == '' and self.process.poll() is not None:
                    break
                if output:
                    logs.append(output.strip())
                    print(output.strip())
                    
            # 等待进程完成
            return_code = self.process.wait()
            
            self._logs = '\n'.join(logs)
            self._completed = True
            
            if return_code == 0:
                self._status = "Succeeded"
            else:
                self._status = "Failed"
                
        except Exception as e:
            self._status = "Failed"
            self._logs = f"Error: {str(e)}"
            self._completed = True
            print(f"Test failed: {e}")
            
    def stop_test(self):
        """停止测试"""
        if self.process:
            self.process.terminate()
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
                
    @property
    def status(self):
        return self._status
        
    @property
    def completed(self):
        return self._completed
        
    @property
    def logs(self):
        return self._logs
        
    @property
    def bandwidth(self):
        if self._bandwidth:
            return self._bandwidth
        if self.completed:
            self._bandwidth = get_bandwidth(self.logs)
            return self._bandwidth
        return "N/A"
        
    def __repr__(self):
        return f"Job {self.job_name} completed: {str(self.completed):<5}, status: {self.status:<10}, bandwidth: {self.bandwidth:<8}, nodes: {self.nodes}"

def get_bandwidth(logs: str) -> str:
    """从日志中提取带宽信息"""
    for log in logs.split("\n"):
        if "Avg bus bandwidth" in log:
            return log.split(":")[-1].strip()
    return "N/A"

def expand_nodes(nodes):
    """扩展节点范围表示法"""
    expanded_nodes = []
    pattern = re.compile(r'\[(\d+)-(\d+)]')
    for node in nodes:
        match = pattern.search(node)
        if match:
            start = int(match.group(1))
            end = int(match.group(2))
            expanded_nodes.extend([f"{node[:match.start()]}{i:02d}" for i in range(start, end + 1)])
        else:
            expanded_nodes.append(node)
    return expanded_nodes

def random_grouping(nodes, n):
    """随机分组节点"""
    lst = deepcopy(nodes)
    random.shuffle(lst)
    grouped_list = [lst[i:i + n] for i in range(0, len(lst), n)]
    
    if len(grouped_list[-1]) < n:
        remainder = n - len(grouped_list[-1])
        choice_list = list(set(lst) - set(grouped_list[-1]))
        if choice_list:
            grouped_list[-1].extend(random.choices(choice_list, k=min(remainder, len(choice_list))))
    
    return grouped_list

def run_group_nccl_test(group_nodes, gpu_num, ib_num, image, nccl_config):
    """运行一组NCCL测试"""
    tmp_jobs = []
    
    for group in group_nodes:
        nccl_test = DockerNcclTest(group, gpu_num, ib_num, image, nccl_config)
        nccl_test.run_test()
        tmp_jobs.append(nccl_test)
        
    return tmp_jobs

def get_summary(jobs) -> str:
    """生成测试摘要"""
    print("Summary:")
    result = "\n"
    result += "| nodes | status | bandwidth |\n"
    result += "|-------|--------|-----------|\n"
    for job in jobs:
        result += f"|{','.join(job.nodes)}|{job.status:<10}|{job.bandwidth:<8}|\n"
    result += "\n"
    print(result)
    return result

def main():
    parser = argparse.ArgumentParser(
        description="Docker-based NCCL test program",
        usage="%(prog)s [options]",
    )
    
    parser.add_argument("--gpu_num", metavar="gpu_num", type=int, default=8,
                        help="the number of gpus to run the test")
    parser.add_argument("--ib_num", metavar="ib_num", type=int, default=1,
                        help="the number of ibs to run the test")
    parser.add_argument('--nodes', metavar='nodes', type=str, nargs='+',
                        help='the list of nodes to run the test, eg: --nodes hs[01-07] hs09')
    parser.add_argument('--image', metavar='image', type=str,
                        default='dockerhub.aicp.local/aicp-common/aicp_common/nccl_test:v3.0',
                        help='the image to run the test')
    parser.add_argument('--mode', metavar='mode', type=str, default="random",
                        help='test mode: random (1,2,4,...) or a number for fixed group size')
    parser.add_argument('--NCCL_NVLS_ENABLE', metavar='NCCL_NVLS_ENABLE', type=str, default="1",
                        choices=["0", "1"], help='NCCL_NVLS_ENABLE value')
    parser.add_argument('--NCCL_NET_GDR_LEVEL', metavar="NCCL_NET_GDR_LEVEL", type=str, default="2",
                        help="NCCL_NET_GDR_LEVEL")
    parser.add_argument('--NCCL_IB_QPS_PER_CONNECTION', metavar="NCCL_IB_QPS_PER_CONNECTION", type=str, default="2",
                        help="NCCL_IB_QPS_PER_CONNECTION")
    parser.add_argument('--NCCL_ALGO', metavar="NCCL_ALGO", type=str,
                        default="Tree,Ring,CollnetDirect,CollnetChain,NVLS,NVLSTree",
                        help="NCCL_ALGO")
    
    args = parser.parse_args()
    
    if not args.nodes:
        print("Error: --nodes argument is required")
        sys.exit(1)
        
    nodes = expand_nodes(args.nodes)
    nccl_config = {
        'NCCL_NVLS_ENABLE': args.NCCL_NVLS_ENABLE,
        'NCCL_NET_GDR_LEVEL': args.NCCL_NET_GDR_LEVEL,
        'NCCL_IB_QPS_PER_CONNECTION': args.NCCL_IB_QPS_PER_CONNECTION,
        'NCCL_ALGO': args.NCCL_ALGO,
    }
    
    # 生成测试报告文件名
    test_report_name = f"docker-test-report-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.md"
    
    try:
        with open(test_report_name, "w", encoding="utf-8") as f:
            f.write("# Docker NCCL Test Report\n")
            f.write(f"Nodes: {','.join(nodes)}\n")
            f.write(f"Image: {args.image}\n")
            f.write(f"GPU: {args.gpu_num}, IB: {args.ib_num}\n\n")
            
            f.write("## Summary\n")
            
            if args.mode.isdigit():
                # 固定分组大小
                group_size = int(args.mode)
                groups = random_grouping(nodes, group_size)
                jobs = run_group_nccl_test(groups, args.gpu_num, args.ib_num, args.image, nccl_config)
                f.write(f"### {group_size} node test\n")
                f.write(get_summary(jobs))
            else:
                # 随机模式：1, 2, 4, 8...
                step = 1
                while step <= len(nodes):
                    groups = random_grouping(nodes, step)
                    jobs = run_group_nccl_test(groups, args.gpu_num, args.ib_num, args.image, nccl_config)
                    f.write(f"### {step} node test\n")
                    f.write(get_summary(jobs))
                    step *= 2
                    
            f.write("\n# Detailed Logs\n")
            for job in jobs:
                f.write(f"## Job {job.job_name}\n")
                f.write(f"Nodes: {job.nodes}\n")
                f.write(f"Status: {job.status}\n")
                f.write(f"Bandwidth: {job.bandwidth}\n")
                f.write(f"Logs:\n```\n{job.logs}\n```\n\n")
                
        print(f"Test report saved to: {test_report_name}")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 