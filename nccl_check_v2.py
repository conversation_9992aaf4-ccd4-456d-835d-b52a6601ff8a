###
# @Author: wxwang
# @Script description: nccl 慢节点筛选，需要在容器内使用
###
import os
import random
import subprocess
import sys
import threading
import time
from queue import Queue
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 全局配置
MAX_RETRIES = 2  # 最大重试次数
MAX_DURATION = 18000  # 300分钟超时(秒)
RETRY_DELAY = 5  # 重试间隔(秒)
MAX_THREADS = 10  # 最大并发线程数
MIN_GROUP_SIZE = 4  # 最小分组大小，小于此值不并发

# 全局日志文件变量
NCCL_LOG_FILE = ""
APP_LOG_FILE = ""


def ensure_logs_dir():
    """确保logs目录存在"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs") if __file__ != "" else os.path.join(os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    return log_dir


def get_log_filenames(node_count):
    """根据节点数量生成日志文件名"""
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = ensure_logs_dir()
    return (
        os.path.join(log_dir, f"nccl_debug_{current_time}_{node_count}n.log"),
        os.path.join(log_dir, f"app_runtime_{current_time}_{node_count}n.log")
    )


def get_timestamp():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def log(message, level="INFO"):
    global APP_LOG_FILE
    log_entry = f"[{get_timestamp()}] [{level}] {message}"
    print(log_entry)
    if APP_LOG_FILE:
        with open(APP_LOG_FILE, 'a') as f:
            f.write(log_entry + '\n')


def log_nccl_output(output, group_name=""):
    """带组标识的NCCL日志记录"""
    global NCCL_LOG_FILE
    if NCCL_LOG_FILE:
        with open(NCCL_LOG_FILE, 'a') as f:
            f.write(f"\n[{get_timestamp()}] [GROUP {group_name}] NCCL OUTPUT:\n")
            f.write(output + '\n')


def build_host_string(ip_list, slots_per_host=8):
    return ",".join([f"{ip}:{slots_per_host}" for ip in ip_list])


def check_ssh_connection(host, timeout=5):
    """检查SSH免密登录"""
    try:
        cmd = ["ssh", "-o", "BatchMode=yes", "-o", "ConnectTimeout=" + str(timeout),
               host, "echo SSH连接测试成功"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            log(f"SSH连接检查通过: {host}")
            return True
        else:
            log(f"SSH连接失败: {host} - 错误: {result.stderr.strip()}", "ERROR")
            return False
    except subprocess.TimeoutExpired:
        log(f"SSH连接超时: {host} (超过{timeout}秒)", "ERROR")
        return False
    except Exception as e:
        log(f"SSH连接异常: {host} - {str(e)}", "ERROR")
        return False


def check_all_nodes_ssh(nodes, max_threads=50):
    """并发SSH检查"""
    log("\n开始SSH免密登录检查...")
    failed_nodes = []

    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        results = list(executor.map(check_ssh_connection, nodes))

    failed_nodes = [node for node, success in zip(nodes, results) if not success]

    if failed_nodes:
        log(f"\nSSH检查失败节点 ({len(failed_nodes)}个): {failed_nodes}", "ERROR")
        log("请确保以下节点已配置SSH免密登录:", "ERROR")
        for node in failed_nodes:
            log(f"  ssh-copy-id {node}", "ERROR")
        return False
    return True


def allreduce_test(node_group, group_name=""):
    """执行NCCL测试并返回结果"""
    slots_per_host = 8
    host_string = build_host_string(node_group, slots_per_host)
    total_processes = len(node_group) * slots_per_host
    test_timeout = 70  # 测试超时时间(秒)

    test_id = f"{group_name}-{time.strftime('%m%d%H%M%S')}-{random.randint(1000,9999)}"

    # 记录测试开始信息
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"\n\n{'='*80}\n")
        f.write(f"[TEST START] Group: {group_name} | ID: {test_id}\n")
        f.write(f"Nodes: {node_group}\n")
        f.write(f"Processes: {total_processes}\n")
        f.write(f"Start Time: {get_timestamp()}\n")
        f.write(f"{'='*80}\n")

    cmd = [
        "mpirun",
        "--allow-run-as-root",
        "-np", str(total_processes),
        "-host", host_string,
        "-x", "NCCL_DEBUG=INFO",
        "-x", "NCCL_IB_HCA=mlx5_100,mlx5_101,mlx5_102,mlx5_103,mlx5_104,mlx5_105,mlx5_106,mlx5_107",
        "-x", "NCCL_ALGO=Ring",
        "-x", "NCCL_SOCKET_IFNAME=bond0",
        "-x", "NCCL_IB_GID_INDEX=3",
        "-x", f"NCCL_TIMEOUT=60",
        "./build/all_reduce_perf",
        "-b", "16G",
        "-e", "16G",
        "-g", "1",
        "-f", "2",
        "-T", "60"
    ]

    log(f"\n[测试 {group_name} | ID: {test_id}] 启动测试组")
    log(f"[测试 {group_name} | ID: {test_id}]   节点列表: {node_group}")
    log(f"[测试 {group_name} | ID: {test_id}]   总进程数: {total_processes}  (机器数: {len(node_group)})")
    log(f"[测试 {group_name} | ID: {test_id}]   完整命令: {' '.join(cmd)}")

    attempt = 0
    while attempt < MAX_RETRIES:
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=test_timeout)
            elapsed = time.time() - start_time

            # 记录详细结果
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST RESULT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Exit Code: {result.returncode}\n")
                f.write(f"Duration: {elapsed:.2f}s\n")
                f.write("=== STDOUT ===\n")
                f.write(result.stdout + "\n")
                if result.stderr:
                    f.write("=== STDERR ===\n")
                    f.write(result.stderr + "\n")

            # 解析带宽
            bandwidth = None
            for line in result.stdout.split('\n'):
                if "Avg bus bandwidth" in line:
                    try:
                        bandwidth = float(line.split()[-1])
                        break
                    except:
                        continue

            success = bandwidth is not None and bandwidth > 70
            status = "通过" if success else "失败"
            log(f"[测试 {group_name} | ID: {test_id}] 测试结果: {status}, 带宽: {bandwidth or '无'} GB/s, 耗时: {elapsed:.2f}s")

            # 记录测试结束
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST END] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Status: {'SUCCESS' if success else 'FAILED'}\n")
                f.write(f"End Time: {get_timestamp()}\n")
                f.write(f"{'='*80}\n")

            return success

        except subprocess.TimeoutExpired as e:
            attempt += 1
            elapsed = time.time() - start_time
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次超时 (耗时{elapsed:.2f}s/{test_timeout}s)", "WARNING")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST TIMEOUT] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Elapsed: {elapsed:.2f}s\n")
                f.write(f"Error: {str(e)}\n")
            time.sleep(RETRY_DELAY)

        except Exception as e:
            attempt += 1
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            log(f"[测试 {group_name} | ID: {test_id}] 第 {attempt}/{MAX_RETRIES} 次异常: {str(e)}", "ERROR")
            with open(NCCL_LOG_FILE, 'a') as f:
                f.write(f"\n[TEST ERROR] Group: {group_name} | ID: {test_id}\n")
                f.write(f"Error Type: {type(e).__name__}\n")
                f.write(f"Error Msg: {str(e)}\n")
            time.sleep(RETRY_DELAY)

    # 所有重试均失败
    with open(NCCL_LOG_FILE, 'a') as f:
        f.write(f"\n[TEST FAILED] Group: {group_name} | ID: {test_id}\n")
        f.write(f"All {MAX_RETRIES} attempts failed\n")
        f.write(f"{'='*80}\n")
    log(f"[测试 {group_name} | ID: {test_id}] 所有重试均失败", "ERROR")
    return False


def prescreen(nodes, group_name="主组"):
    """并发二分筛查主逻辑"""
    log(f"\n[筛查 {group_name}] 开始节点健康检查，共 {len(nodes)} 个节点: {nodes}")
    suspects = nodes.copy()
    witnesses = []
    start_time = time.time()
    retry_count = 0

    while len(suspects) > 3 and (time.time() - start_time) < MAX_DURATION:
        random.shuffle(suspects)
        current_group = suspects.copy()

        # 测试完整组
        log(f"\n[筛查 {group_name}] ============ 开始测试完整节点组 ============")
        full_group_success = allreduce_test(current_group, group_name=f"{group_name}-完整组")

        if full_group_success:
            witnesses.extend(current_group)
            suspects = []
            break

        # 二分测试
        mid = len(suspects) // 2
        left, right = suspects[:mid], suspects[mid:]

        log(f"\n[筛查 {group_name}] ============ 开始并发二分筛查 ============")
        log(f"[筛查 {group_name}] 左子组节点: {left}")
        log(f"[筛查 {group_name}] 右子组节点: {right}")

        # 并发测试两个子组
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = {
                executor.submit(allreduce_test, left, f"{group_name}-左子组"): "left",
                executor.submit(allreduce_test, right, f"{group_name}-右子组"): "right"
            }

            results = {}
            for future in futures:
                side = futures[future]
                try:
                    results[side] = future.result()
                except Exception as e:
                    log(f"[筛查 {group_name}] {side}子组测试异常: {str(e)}", "ERROR")
                    results[side] = False

        left_ok = results.get("left", False)
        right_ok = results.get("right", False)

        # 处理结果
        if left_ok and right_ok:
            retry_count += 1
            if retry_count >= MAX_RETRIES:
                log(f"[筛查 {group_name}] 已达到最大重试次数({MAX_RETRIES})，停止重试", "WARNING")
                suspects = current_group
                break
            else:
                log(f"[筛查 {group_name}] 两组测试均通过但完整组失败(重试{retry_count}/{MAX_RETRIES})")
                time.sleep(RETRY_DELAY)
                continue
        elif left_ok:
            log(f"[筛查 {group_name}] 左子组测试通过，标记为正常节点")
            witnesses.extend(left)
            suspects = right
        elif right_ok:
            log(f"[筛查 {group_name}] 右子组测试通过，标记为正常节点")
            witnesses.extend(right)
            suspects = left
        else:
            log(f"[筛查 {group_name}] 两组测试均失败，将对两个子组都进行并发筛查")
            with ThreadPoolExecutor(max_workers=2) as executor:
                future_left = executor.submit(prescreen, left, f"{group_name}-左子组")
                future_right = executor.submit(prescreen, right, f"{group_name}-右子组")

                left_suspects, left_witnesses = future_left.result()
                right_suspects, right_witnesses = future_right.result()

            witnesses.extend(left_witnesses)
            witnesses.extend(right_witnesses)
            suspects = left_suspects + right_suspects
            break

    # 处理剩余节点（3个或更少）
    if len(suspects) == 3:
        log(f"\n[筛查 {group_name}] ============ 处理最后3个节点 ============")
        test_group = suspects[:2]
        log(f"[筛查 {group_name}] 测试2节点组合: {test_group}")

        success = allreduce_test(test_group, group_name=f"{group_name}-最后3节点中的2节点组")

        if success:
            log(f"[筛查 {group_name}] 2节点组合测试通过，将单独验证第3个节点")
            validation_group = test_group + [suspects[2]]
            log(f"[筛查 {group_name}] 验证组合: {validation_group}")

            validation_ok = allreduce_test(validation_group, group_name=f"{group_name}-验证第3节点")

            if validation_ok:
                witnesses.extend(suspects)
                suspects = []
            else:
                witnesses.extend(test_group)
                suspects = [suspects[2]]
        else:
            log(f"[筛查 {group_name}] 2节点组合测试失败，3个节点都需要进一步筛查")

    elif len(suspects) == 2:
        log(f"\n[筛查 {group_name}] ============ 最终2个节点测试 ============")
        log(f"[筛查 {group_name}] 待确认节点: {suspects}")

        success = allreduce_test(suspects, group_name=f"{group_name}-最终2节点组")

        if success:
            witnesses.extend(suspects)
            suspects = []
        else:
            log(f"[筛查 {group_name}] 两个节点一起测试失败，无法确定具体问题节点", "WARNING")

    elif len(suspects) == 1:
        log(f"\n[筛查 {group_name}] ============ 剩余1个节点 ============")
        log(f"[筛查 {group_name}] 单个节点 {suspects[0]} 无法单独测试，需要人工检查", "WARNING")

    log(f"\n[筛查 {group_name}] ============ 筛查完成 ============")
    log(f"[筛查 {group_name}] 可疑节点: {suspects}")
    log(f"[筛查 {group_name}] 正常节点: {witnesses}")
    return suspects, witnesses


def validate_suspects(suspects, witnesses):
    """并发验证可疑节点"""
    if not suspects:
        log("\n[验证阶段] 没有可疑节点需要验证", "INFO")
        return suspects, witnesses

    if not witnesses:
        log("\n[验证阶段] 没有正常节点可用于验证可疑节点", "WARNING")
        return suspects, witnesses

    log("\n[验证阶段] ============ 开始并发验证可疑节点 ============")
    confirmed_good = []
    confirmed_bad = []
    lock = threading.Lock()
    available_witnesses = witnesses.copy()

    def validate_node(suspect):
        nonlocal available_witnesses
        with lock:
            if len(available_witnesses) < 2:
                log(f"[验证阶段] 正常节点不足，无法验证节点 {suspect}", "WARNING")
                return suspect, None

            test_witnesses = available_witnesses[:2]
            available_witnesses = available_witnesses[2:]

        test_group = test_witnesses + [suspect]
        group_name = f"验证组-{suspect}"
        test_id = f"{group_name}-{time.strftime('%m%d%H%M%S')}-{random.randint(1000,9999)}"
        log(f"[{group_name} | ID: {test_id}] 开始验证: 使用正常节点 {test_witnesses} 测试可疑节点 {suspect}")
        log(f"[{group_name} | ID: {test_id}]   节点列表: {test_group}")
        log(f"[{group_name} | ID: {test_id}]   总进程数: {len(test_group)*8}  (机器数: {len(test_group)})")

        success = allreduce_test(test_group, group_name=group_name)

        if success:
            log(f"[{group_name} | ID: {test_id}] 节点 {suspect} 验证通过")
            return suspect, True
        else:
            log(f"[{group_name} | ID: {test_id}] 确认问题节点: {suspect}", "ERROR")
            return suspect, False

    with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
        futures = [executor.submit(validate_node, suspect) for suspect in suspects]

        for future in futures:
            try:
                suspect, result = future.result()
                if result is not None:
                    if result:
                        confirmed_good.append(suspect)
                    else:
                        confirmed_bad.append(suspect)
            except Exception as e:
                log(f"[验证阶段] 验证任务异常: {str(e)}", "ERROR")

    # 更新节点状态
    witnesses.extend(confirmed_good)
    suspects = confirmed_bad

    return suspects, witnesses


def main():
    global NCCL_LOG_FILE, APP_LOG_FILE

    if len(sys.argv) != 2:
        print("使用方法: python allreduce_checker.py <节点列表文件路径>")
        sys.exit(1)

    # 读取节点列表
    try:
        with open(sys.argv[1], 'r') as file:
            nodes = [line.strip() for line in file if line.strip()]
    except Exception as e:
        print(f"无法读取节点文件: {str(e)}")
        sys.exit(1)

    if not nodes:
        print("错误: 节点列表为空")
        sys.exit(1)

    # 设置日志文件
    NCCL_LOG_FILE, APP_LOG_FILE = get_log_filenames(len(nodes))

    with open(NCCL_LOG_FILE, 'w') as f:
        f.write(f"NCCL调试日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")
    with open(APP_LOG_FILE, 'w') as f:
        f.write(f"应用运行日志 - 开始时间 {get_timestamp()}\n")
        f.write(f"节点数量: {len(nodes)}\n")

    log(f"从文件成功加载 {len(nodes)} 个节点")
    start_time = time.time()

    # 检查SSH连通性
    if not check_all_nodes_ssh(nodes):
        log("SSH免密登录检查未通过，程序终止", "ERROR")
        sys.exit(1)

    # 主筛查流程
    suspects, witnesses = prescreen(nodes)
    suspects, witnesses = validate_suspects(suspects, witnesses)

    # 结果汇总
    log(f"\n============ 测试总结 ============")
    log(f"总耗时: {time.time() - start_time:.2f}秒")
    log("\n============ 节点状态汇总 ============")
    log(f"正常节点 ({len(witnesses)}个): {sorted(witnesses)}")
    log(f"异常节点 ({len(suspects)}个): {sorted(suspects)}", "ERROR" if suspects else "INFO")

    log("\n============ 日志文件 ============")
    log(f"NCCL调试日志已保存至: {NCCL_LOG_FILE}")
    log(f"应用运行日志已保存至: {APP_LOG_FILE}")


if __name__ == "__main__":
    main()