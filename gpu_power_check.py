#!/usr/bin/env python3
"""
GPU Power Validation Tool
修复内容：
1. 解决监控数据无法正确汇总的问题
2. 增强线程安全性和超时处理
3. 优化日志输出和错误处理
4. 支持动态TDP检测和掉卡检测
5. 改进gpu_burn可执行文件查找逻辑
"""

import subprocess
import time
import logging
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError
import argparse
import sys
import shutil
import os
from typing import Dict, List, Optional

# 配置常量
LOG_DIR = Path("logs")
DEFAULT_TEST_DURATION = 300  # 默认测试时长(秒)
SAFE_TEMP_LIMIT = 90  # 温度熔断阈值(℃)
POWER_THRESHOLD_RATIO = 0.99  # 功率达标阈值(达到TDP的95%算达标)

# 内置GPU TDP配置（单位：瓦特）
BUILTIN_TDP_CONFIG = {
    "NVIDIA H100 80GB HBM3": 700,
    "NVIDIA A100-SXM4-80GB": 400,
    "NVIDIA H100-PCIE-80GB": 350,
    "Tesla V100-SXM2-32GB": 300,
    "Tesla T4": 70,
    "RTX 4090": 450
}

class GPUPowerValidator:
    def __init__(self, test_duration=DEFAULT_TEST_DURATION, debug=False, burn_path: Optional[str] = None):
        self.test_duration = test_duration
        self.gpu_info: List[Dict] = []
        self.original_gpu_count = 0
        self.burn_proc = None
        self.debug = debug
        self.burn_path = burn_path
        self.setup_logging()

    def setup_logging(self):
        """配置日志系统"""
        LOG_DIR.mkdir(exist_ok=True)
        log_level = logging.DEBUG if self.debug else logging.INFO

        logging.basicConfig(
            filename=LOG_DIR / f"gpu_power_{time.strftime('%Y%m%d')}.log",
            level=log_level,
            format='%(asctime)s [%(levelname)s] %(message)s',
            filemode='a'
        )

        # 控制台输出
        console = logging.StreamHandler()
        console.setLevel(log_level)
        formatter = logging.Formatter('%(message)s')
        console.setFormatter(formatter)
        logging.getLogger().addHandler(console)

        logging.info(f"GPU Power Validator - (启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')})")
        logging.info("="*60)

    def detect_gpus(self) -> int:
        """检测所有GPU设备并初始化信息"""
        try:
            cmd = [
                "nvidia-smi",
                "--query-gpu=index,name,serial,power.limit",
                "--format=csv,noheader"
            ]
            output = subprocess.check_output(cmd, timeout=15).decode().strip()

            self.gpu_info = []
            for line in output.split('\n'):
                if not line.strip():
                    continue

                parts = [p.strip() for p in line.split(',')]
                if len(parts) < 4:
                    continue

                gpu = {
                    "id": parts[0],
                    "model": parts[1],
                    "sn": parts[2],
                    "power_limit": float(parts[3].split()[0]),
                    "max_power": 0.0,
                    "max_temp": 0,
                    "status": "PENDING",
                    "messages": [],
                    "power_ok": False,
                    "disconnected": False
                }

                # 动态确定预期TDP
                gpu["expected_tdp"] = BUILTIN_TDP_CONFIG.get(
                    gpu["model"],
                    gpu["power_limit"]  # 默认使用实际功率限制
                )
                self.gpu_info.append(gpu)

            self.original_gpu_count = len(self.gpu_info)
            logging.info(f"检测到 {self.original_gpu_count} 块GPU:")

            for gpu in self.gpu_info:
                logging.info(
                    f"  GPU{gpu['id']}: {gpu['model']} (SN: {gpu['sn']})\n"
                    f"    功率限制: {gpu['power_limit']}W | "
                    f"预期TDP: {gpu['expected_tdp']}W"
                )

            return self.original_gpu_count

        except subprocess.TimeoutExpired:
            logging.error("GPU检测超时，请检查nvidia-smi是否正常工作")
            return 0
        except Exception as e:
            logging.error(f"GPU检测失败: {str(e)}")
            return 0

    def find_gpu_burn(self) -> Optional[str]:
        """查找gpu_burn可执行文件"""
        # 如果用户已指定路径，直接使用
        if self.burn_path:
            if Path(self.burn_path).exists():
                return self.burn_path
            logging.warning(f"指定的gpu_burn路径不存在: {self.burn_path}")
            return None

        # 尝试从多个可能的位置查找
        possible_paths = [
            # 当前目录下的
            "./gpu_burn",
            # 系统路径
            "/usr/local/bin/gpu_burn",
            "/usr/bin/gpu_burn",
            # 用户目录
            os.path.expanduser("~/bin/gpu_burn"),
            os.path.expanduser("~/.local/bin/gpu_burn"),
            # 通过which查找
            shutil.which("gpu_burn"),
            # 脚本所在目录
            Path(__file__).parent / "gpu_burn",
            Path(__file__).parent.parent / "gpu_burn",
        ]

        for path in possible_paths:
            if not path:
                continue
            try:
                path = Path(path).resolve()
                if path.exists():
                    logging.debug(f"找到gpu_burn: {path}")
                    return str(path)
            except Exception as e:
                logging.debug(f"检查路径 {path} 失败: {str(e)}")
                continue

        return None

    def start_stress_test(self) -> bool:
        """启动GPU压力测试"""
        try:
            burn_path = self.find_gpu_burn()
            if not burn_path:
                raise FileNotFoundError(
                    "未找到gpu_burn可执行文件\n"
                    "请确保gpu_burn已安装并位于以下位置之一:\n"
                    "  - 当前目录\n"
                    "  - /usr/local/bin/\n"
                    "  - /usr/bin/\n"
                    "  - ~/bin/\n"
                    "或使用--burn-path参数指定路径"
                )

            cmd = [burn_path, "-d", str(self.test_duration)]
            logging.info(f"\n启动压力测试: {' '.join(cmd)}")

            self.burn_proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=Path(burn_path).parent  # 在gpu_burn所在目录运行
            )

            # 启动日志记录线程
            ThreadPoolExecutor().submit(self.log_process_output)
            return True

        except Exception as e:
            logging.error(f"启动压力测试失败: {str(e)}")
            return False

    def log_process_output(self):
        """记录gpu_burn的输出"""
        try:
            for line in iter(self.burn_proc.stdout.readline, ''):
                logging.debug(f"[gpu_burn] {line.strip()}")
        except Exception as e:
            logging.warning(f"日志记录异常: {str(e)}")

    def monitor_gpu(self, gpu_id: str) -> tuple:
        """监控单个GPU的功耗和温度（线程安全版）"""
        power_cmd = [
            "nvidia-smi",
            "-i", gpu_id,
            "--query-gpu=power.draw,temperature.gpu,utilization.gpu",
            "--format=csv,noheader,nounits",
            "-l", "1"  # 每秒采样一次
        ]

        max_power = 0.0
        max_temp = 0
        start_time = time.time()
        got_data = False

        try:
            proc = subprocess.Popen(
                power_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            while time.time() - start_time < self.test_duration:
                line = proc.stdout.readline().strip()
                if not line:
                    if proc.poll() is not None:
                        break
                    time.sleep(0.1)
                    continue

                logging.debug(f"GPU{gpu_id} 原始数据: {line}")

                try:
                    parts = line.split(',')
                    if len(parts) < 2:
                        continue

                    power = float(parts[0])
                    temp = int(float(parts[1]))
                    utilization = int(parts[2]) if len(parts) > 2 else 0

                    max_power = max(max_power, power)
                    max_temp = max(max_temp, temp)
                    got_data = True

                    if temp > SAFE_TEMP_LIMIT:
                        proc.terminate()
                        return (max_power, max_temp, "OVERHEAT",
                                f"温度超过安全限制: {temp}℃ > {SAFE_TEMP_LIMIT}℃")

                    # 调试信息
                    if self.debug and utilization < 90:
                        logging.warning(
                            f"GPU{gpu_id} 负载不足: {utilization}% "
                            f"(当前功率: {power:.1f}W)"
                        )

                except ValueError as e:
                    logging.warning(f"GPU{gpu_id} 数据解析失败: {line} ({str(e)})")
                    continue

            proc.terminate()

            if not got_data:
                return (0.0, 0, "ERROR", "未获取到有效监控数据")

            return (max_power, max_temp, "COMPLETED", "测试完成")

        except Exception as e:
            logging.error(f"GPU{gpu_id} 监控异常: {str(e)}")
            return (0.0, 0, "ERROR", str(e))

    def run_test(self) -> bool:
        """执行完整测试流程"""
        if self.detect_gpus() == 0:
            return False

        if not self.start_stress_test():
            return False

        # 并行监控所有GPU
        with ThreadPoolExecutor(max_workers=len(self.gpu_info)) as executor:
            future_to_gpu = {
                executor.submit(self.monitor_gpu, gpu["id"]): gpu["id"]
                for gpu in self.gpu_info
            }

            try:
                for future in as_completed(future_to_gpu, timeout=self.test_duration + 30):
                    gpu_id = future_to_gpu[future]
                    try:
                        max_power, max_temp, status, message = future.result()

                        # 更新GPU信息
                        for gpu in self.gpu_info:
                            if gpu["id"] == gpu_id:
                                gpu.update({
                                    "max_power": max_power,
                                    "max_temp": max_temp,
                                    "status": status,
                                    "messages": [message]
                                })
                                logging.info(
                                    f"GPU{gpu_id} 监控完成: "
                                    f"{max_power:.1f}W/{gpu['expected_tdp']}W, "
                                    f"{max_temp}℃, {status}"
                                )
                    except Exception as e:
                        logging.error(f"GPU{gpu_id} 结果处理失败: {str(e)}")
            except TimeoutError:
                logging.error("监控线程超时，强制终止测试")
                self.emergency_shutdown()
                return False

        # 检查是否有GPU过热
        if any(g["status"] == "OVERHEAT" for g in self.gpu_info):
            self.emergency_shutdown()

        # 检查掉卡情况
        self.check_gpu_disconnection()

        # 结果分析
        return self.analyze_results()

    def emergency_shutdown(self):
        """紧急关闭测试"""
        logging.critical("\n执行紧急关闭...")
        if self.burn_proc and self.burn_proc.poll() is None:
            self.burn_proc.terminate()
            try:
                self.burn_proc.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.burn_proc.kill()

    def check_gpu_disconnection(self):
        """检查测试过程中是否有GPU掉卡"""
        try:
            cmd = [
                "nvidia-smi",
                "--query-gpu=index",
                "--format=csv,noheader"
            ]
            current_gpus = set(
                subprocess.check_output(cmd, timeout=10)
                .decode().strip().split('\n')
            )

            original_ids = {g["id"] for g in self.gpu_info}
            lost_gpus = original_ids - current_gpus

            if lost_gpus:
                for gpu in self.gpu_info:
                    if gpu["id"] in lost_gpus:
                        gpu.update({
                            "status": "DISCONNECTED",
                            "disconnected": True,
                            "messages": ["测试过程中掉卡"]
                        })
                logging.warning(
                    f"检测到掉卡: 丢失GPU {lost_gpus}\n"
                    f"原始数量: {self.original_gpu_count}, "
                    f"当前数量: {len(current_gpus)}"
                )
        except Exception as e:
            logging.error(f"掉卡检测失败: {str(e)}")

    def analyze_results(self) -> bool:
        overall_pass = True
        underpowered_gpus = []
        disconnected_gpus = []
        high_temp_gpus = []  # 新增：记录温度异常的GPU
    
        # 计算有效GPU的平均功率和温度
        valid_gpus = [g for g in self.gpu_info if g["status"] in ["COMPLETED", "OVERHEAT"]]
        avg_power = sum(g["max_power"] for g in valid_gpus) / len(valid_gpus) if valid_gpus else 0
        avg_temp = sum(g["max_temp"] for g in valid_gpus) / len(valid_gpus) if valid_gpus else 0  # 新增：计算平均温度
    
        for gpu in self.gpu_info:
            # 处理掉卡情况
            if gpu.get("disconnected"):
                gpu["result"] = "FAIL"
                disconnected_gpus.append(gpu["id"])
                overall_pass = False
                continue
    
            # 跳过异常状态
            if gpu["status"] not in ["COMPLETED", "OVERHEAT"]:
                gpu["result"] = "ERROR"
                overall_pass = False
                continue
    
            expected = gpu["expected_tdp"]
            actual = gpu["max_power"]
            temp = gpu["max_temp"]  # 获取最高温度
    
            # 判断功率是否达标
            power_ok = actual >= expected * POWER_THRESHOLD_RATIO
            gpu["power_ok"] = power_ok
    
            # 新增：检查温度是否明显高于平均值（超过20度）
            if valid_gpus and temp > avg_temp + 20:
                high_temp_gpus.append(gpu["id"])
                gpu["messages"].append(
                    f"温度明显高于其他卡: {temp}℃ (平均: {avg_temp:.1f}℃)"
                )
                gpu["result"] = "FAIL"
                overall_pass = False
                continue
    
            # 判断是否明显低于其他卡
            if valid_gpus and actual < avg_power * 0.95:
                underpowered_gpus.append(gpu["id"])
                gpu["messages"].append(
                    f"功率明显低于其他卡: {actual:.1f}W (平均: {avg_power:.1f}W)"
                )
    
            # 结果判定
            if gpu["status"] == "OVERHEAT":
                gpu["result"] = "FAIL"
                overall_pass = False
            elif power_ok:
                gpu["result"] = "PASS"
            else:
                gpu["result"] = "FAIL"
                overall_pass = False
                gpu["messages"].append(
                    f"功率不足: {actual:.1f}W (预期: {expected}W的{POWER_THRESHOLD_RATIO*100:.0f}%以上)"
                )
    
        # 输出异常情况
        if underpowered_gpus:
            logging.warning("\n" + "!"*50)
            logging.warning(f" 警告: 检测到功率明显偏低的GPU: {underpowered_gpus}")
            logging.warning(" 可能原因: 散热问题、电源供电不足或硬件故障")
            logging.warning("!"*50)
    
        # 新增：输出温度异常警告
        if high_temp_gpus:
            logging.warning("\n" + "!"*50)
            logging.warning(f" 警告: 检测到温度明显偏高的GPU: {high_temp_gpus}")
            logging.warning(" 可能原因: 散热器异常、风扇故障或散热膏老化")
            logging.warning("!"*50)
    
        if disconnected_gpus:
            logging.warning("\n" + "!"*50)
            logging.warning(f" 严重警告: 检测到掉卡的GPU: {disconnected_gpus}")
            logging.warning(" 可能原因: 硬件连接问题、电源故障或驱动崩溃")
            logging.warning("!"*50)
    
        return overall_pass

    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print(f"GPU功率测试结果摘要 (原始GPU数量: {self.original_gpu_count})")
        print("="*60)

        for gpu in self.gpu_info:
            if gpu.get("disconnected"):
                status = "×"
                print(
                    f"GPU{gpu['id']} [{status}] ***掉卡*** {gpu['model']} "
                    f"(SN: {gpu['sn']})"
                )
            else:
                status = "yes" if gpu.get("result") == "PASS" else "no"
                print(
                    f"GPU{gpu['id']} [{status}] {gpu['model']}: "
                    f"{gpu['max_power']:.1f}W/{gpu['expected_tdp']}W, "
                    f"{gpu['max_temp']}℃, {gpu['status']}"
                )

            if gpu["messages"]:
                print("  → " + "; ".join(gpu["messages"]))

        print("\n详细日志请查看:", LOG_DIR / f"gpu_power_{time.strftime('%Y%m%d')}.log")

def main():
    parser = argparse.ArgumentParser(
        description="GPU功率验证工具（支持H100/A100等型号）",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "-t", "--time",
        type=int,
        default=DEFAULT_TEST_DURATION,
        help="测试持续时间（秒）"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式（输出详细日志）"
    )
    parser.add_argument(
        "--burn-path",
        type=str,
        help="手动指定gpu_burn可执行文件路径"
    )

    args = parser.parse_args()

    print("="*60)
    print(f"GPU功率验证工具启动 (测试时长: {args.time}秒)")

    validator = GPUPowerValidator(args.time, args.debug, args.burn_path)
    try:
        test_passed = validator.run_test()
        validator.print_summary()
        sys.exit(0 if test_passed else 1)
    except KeyboardInterrupt:
        validator.emergency_shutdown()
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logging.error(f"测试发生致命错误: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()